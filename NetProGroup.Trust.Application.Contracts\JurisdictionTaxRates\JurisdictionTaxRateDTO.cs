﻿// <copyright file="JurisdictionTaxRateDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;

namespace NetProGroup.Trust.Application.Contracts.JurisdictionTaxRates
{
    /// <summary>
    /// Data Transfer Object for JurisdictionTaxRate.
    /// </summary>
    public class JurisdictionTaxRateDTO : EntityDTO<Guid>
    {
        /// <summary>
        /// Gets or sets the ID of the jurisdiction.
        /// </summary>
        public Guid JurisdictionId { get; set; }

        /// <summary>
        /// Gets or sets the tax rate.
        /// </summary>
        public decimal TaxRate { get; set; }

        /// <summary>
        /// Gets or sets the start date of the tax rate.
        /// </summary>
        public DateTime StartDate { get; set; }
    }
}
