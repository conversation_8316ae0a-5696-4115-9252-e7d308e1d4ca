// <copyright file="AnnouncementDataManager.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Framework.Services.Communication;
using NetProGroup.Framework.Services.Documents;
using NetProGroup.Framework.Services.Identity.EFModels;
using NetProGroup.Framework.Services.Identity.Repository;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.Application.Contracts.Announcements;
using NetProGroup.Trust.Application.Contracts.Communication;
using NetProGroup.Trust.Application.Contracts.Errors;
using NetProGroup.Trust.Application.Contracts.Shared;
using NetProGroup.Trust.DataManager.Bulk;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.DataManager.Security;
using NetProGroup.Trust.Domain.Announcements;
using NetProGroup.Trust.Domain.Inboxes;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Domain.Repository.Extensions;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.Domain.Shared.MessageTemplates;
using NetProGroup.Trust.Domain.Shared.Roles;
using NetProGroup.Trust.DomainShared.Enums;
using System.Linq.Expressions;
using X.PagedList;

namespace NetProGroup.Trust.DataManager.Announcements
{
    /// <summary>
    /// The datamanager for announcements.
    /// </summary>
    public class AnnouncementDataManager : IAnnouncementDataManager
    {
        private readonly ILogger _logger;
        private readonly IMapper _mapper;
        private readonly IAnnouncementsRepository _announcementsRepository;
        private readonly IAnnouncementDocumentsRepository _announcementDocumentsRepository;
        private readonly IAnnouncementRecipientsRepository _announcementRecipientsRepository;
        private readonly IDocumentManager _documentManager;
        private readonly ICommunicationAppService _communicationAppService;
        private readonly IInboxOwnersRepository _inboxOwnersRepository;
        private readonly IInboxService _inboxService;
        private readonly IJurisdictionsRepository _jurisdictionsRepository;
        private readonly ILegalEntitiesRepository _legalEntitiesRepository;
        private readonly IMasterClientsRepository _masterClientsRepository;
        private readonly IUserRepository _userRepository;
        private readonly IBulkOperationProvider _bulkOperationProvider;
        private readonly ISecurityManager _securityManager;

        /// <summary>
        /// Initializes a new instance of the <see cref="AnnouncementDataManager"/> class.
        /// </summary>
        /// <param name="logger">The logger instance.</param>
        /// <param name="mapper">The mapper instance.</param>
        /// <param name="announcementsRepository">The announcement repository.</param>
        /// <param name="announcementDocumentsRepository">The announcement document repository.</param>
        /// <param name="announcementRecipientsRepository">The announcement recipient repository.</param>
        /// <param name="documentManager">The document manager.</param>
        /// <param name="communicationAppService">The communication app service.</param>
        /// <param name="inboxOwnersRepository">The inbox owner repository.</param>
        /// <param name="inboxService">The inbox service.</param>
        /// <param name="jurisdictionsRepository">The jurisdiction repository.</param>
        /// <param name="legalEntitiesRepository">The legal entity repository.</param>
        /// <param name="masterClientsRepository">The repository for master clients.</param>
        /// <param name="userRepository">The user repository.</param>
        /// <param name="bulkOperationProvider">The bulk operation provider.</param>
        /// <param name="securityManager">The security manager.</param>
        public AnnouncementDataManager(
            ILogger<AnnouncementDataManager> logger,
            IMapper mapper,
            IAnnouncementsRepository announcementsRepository,
            IAnnouncementDocumentsRepository announcementDocumentsRepository,
            IAnnouncementRecipientsRepository announcementRecipientsRepository,
            IDocumentManager documentManager,
            ICommunicationAppService communicationAppService,
            IInboxOwnersRepository inboxOwnersRepository,
            IInboxService inboxService,
            IJurisdictionsRepository jurisdictionsRepository,
            ILegalEntitiesRepository legalEntitiesRepository,
            IMasterClientsRepository masterClientsRepository,
            IUserRepository userRepository,
            IBulkOperationProvider bulkOperationProvider,
            ISecurityManager securityManager)
        {
            _logger = logger;
            _mapper = mapper;

            _announcementsRepository = announcementsRepository;
            _announcementDocumentsRepository = announcementDocumentsRepository;
            _announcementRecipientsRepository = announcementRecipientsRepository;
            _documentManager = documentManager;
            _communicationAppService = communicationAppService;
            _inboxOwnersRepository = inboxOwnersRepository;
            _inboxService = inboxService;
            _jurisdictionsRepository = jurisdictionsRepository;
            _legalEntitiesRepository = legalEntitiesRepository;
            _masterClientsRepository = masterClientsRepository;
            _userRepository = userRepository;
            _bulkOperationProvider = bulkOperationProvider;
            _securityManager = securityManager;
        }

        /// <inheritdoc/>
        public async Task<IPagedList<ListAnnouncementDTO>> FilterAnnouncementsAsync(FilterAnnouncementsDTO data)
        {
            ArgumentNullException.ThrowIfNull(data, nameof(data));

            // Check for companies that match the criteria
            var announcementPredicate = GetAnnouncementPredicate(data);

            var masterClients = _announcementsRepository.DbContext.Set<MasterClient>().AsQueryable();
            var jurisdictions = _announcementsRepository.DbContext.Set<Jurisdiction>().AsQueryable();
            var announcementsSet = _announcementsRepository.DbContext.Set<Announcement>().Where(announcementPredicate)
                .Select(announcement => new AnnouncementMappingIntermediateDTO
                {
                    Announcement = announcement,
                    MasterClientCodes = masterClients
                        .Where(client => announcement.Recipients.Any(
                            recipient => recipient.Type == nameof(MasterClient) &&
                                         recipient.RecipientId == client.Id))
                        .Select(client => client.Code).ToList(),
                    JurisdictionNames = jurisdictions
                        .Where(jurisdiction => announcement.Recipients.Any(
                            recipient => recipient.Type == nameof(Jurisdiction) &&
                                         recipient.RecipientId == jurisdiction.Id))
                        .Select(jurisdiction => jurisdiction.Name).ToList()
                });

            var announcements = await announcementsSet.FindByConditionAsPagedListMappedAsync<AnnouncementMappingIntermediateDTO, ListAnnouncementDTO>(
                dto => true,
                _mapper.ConfigurationProvider,
                pageNumber: data.PageNumber,
                pageSize: data.PageSize,
                options: q => q.TagWithCallSite(),
                optionsMapped: q => ApplySorting(q, data.ToSortingInfo()));

            return announcements;
        }

        /// <inheritdoc/>
        public async Task<Guid> CreateUpdateAnnouncementAsync(CreateUpdateAnnouncementDTO data)
        {
            // Checks and validations
            ArgumentNullException.ThrowIfNull(data, nameof(data));

            await ValidateAnnouncementData(data);

            // Check for an existing db transaction and create one if not exists
            var dbTransaction = _announcementsRepository.GetCurrentTransaction();

            bool handleTransaction = false;

            if (dbTransaction == null)
            {
                dbTransaction = await _announcementsRepository.BeginTransactionAsync();
                handleTransaction = true;
            }

            try
            {
                if (data.Id.HasValue)
                {
                    // Retrieve the annonuncement entity
                    // Check the announcement entity
                    var announcement = await _announcementsRepository.CheckAnnouncementByIdAsync(data.Id.Value);

                    // Check the status of the announcement
                    if (announcement.Status == AnnouncementStatus.Sent)
                    {
                        throw new BadRequestException(
                            ApplicationErrors.INVALID_ANNOUNCEMENT_STATUS.ToErrorCode(),
                            $"The announcement with id '{announcement.Id}' cannot be updated because it is already sent.");
                    }

                    announcement.Subject = data.Subject;
                    announcement.EmailSubject = data.EmailSubject;
                    announcement.Body = data.Body;
                    announcement.SendAt = !data.SendNow ? data.SendAt.Value : DateTime.UtcNow;

                    // Create the announcement recipient entities.
                    await ConfigureAnnouncementRecipientsAsync(announcement.Id, data);

                    // Remove the deleted announcement recipients
                    if (data.SendToAllMasterClients)
                    {
                        if (!data.SendToAllJurisdictions)
                        {
                            // If the announcement is set to be sent to all master clients this will delete all recipients that are not with the type MasterClient
                            var deletedAnnouncementRecipients = await _announcementRecipientsRepository.FindByConditionAsync(
                                ao => ao.Type == nameof(Jurisdiction) &&
                                ao.RecipientId != data.JurisdictionId &&
                                ao.AnnouncementId == announcement.Id);

                            await _announcementRecipientsRepository.DeleteAsync(deletedAnnouncementRecipients, true);
                        }
                    }
                    else
                    {
                        // Retrieve the selected masterclients and select the ids
                        var masterClientIds = (await _masterClientsRepository.FindByConditionAsync(mc => data.MasterClientCodes.Contains(mc.Code))).Select(mc => mc.Id);

                        var deletedAnnouncementRecipients = await _announcementRecipientsRepository.FindByConditionAsync(
                            ao => (ao.AnnouncementId == announcement.Id) &&
                                ((!masterClientIds.Contains(ao.RecipientId) && ao.Type == nameof(MasterClient)) ||
                                (!data.LegalEntityIds.Contains(ao.RecipientId) && ao.Type == nameof(LegalEntity)) ||
                                (!data.UserIds.Contains(ao.RecipientId) && ao.Type == nameof(ApplicationUser))));

                        await _announcementRecipientsRepository.DeleteAsync(deletedAnnouncementRecipients, true);
                    }

                    // Check if the announcement will include documents
                    // The announcement document will be created usign a separate endpoint.
                    if (data.IncludeAttachments)
                    {
                        announcement.Status = AnnouncementStatus.Draft;
                        await _announcementsRepository.UpdateAsync(announcement, true);
                    }
                    else
                    {
                        if (data.SendNow)
                        {
                            // Send the announcement now
                            await SendAnnouncementAsync(announcement.Id, true);
                        }
                        else
                        {
                            announcement.Status = AnnouncementStatus.Scheduled;
                            await _announcementsRepository.UpdateAsync(announcement, true);
                        }
                    }

                    if (handleTransaction)
                    {
                        await dbTransaction.CommitAsync();
                    }

                    return announcement.Id;
                }
                else
                {
                    // Create the announcement entity
                    var announcement = new Announcement()
                    {
                        Subject = data.Subject,
                        EmailSubject = data.EmailSubject,
                        Body = data.Body,
                        SendAt = !data.SendNow ? data.SendAt.Value : DateTime.UtcNow
                    };

                    await _announcementsRepository.InsertAsync(announcement, true);

                    // Create the announcement recipient entities.
                    await ConfigureAnnouncementRecipientsAsync(announcement.Id, data);

                    // Check if the announcement will include documents
                    // The announcement document will be created usign a separate endpoint.
                    if (data.IncludeAttachments)
                    {
                        announcement.Status = AnnouncementStatus.Draft;
                        await _announcementsRepository.UpdateAsync(announcement, true);
                    }
                    else
                    {
                        if (data.SendNow)
                        {
                            // Send the announcement now
                            await SendAnnouncementAsync(announcement.Id, true);
                        }
                        else
                        {
                            announcement.Status = AnnouncementStatus.Scheduled;
                            await _announcementsRepository.UpdateAsync(announcement, true);
                        }
                    }

                    if (handleTransaction)
                    {
                        await dbTransaction.CommitAsync();
                    }

                    return announcement.Id;
                }
            }
            catch (Exception ex)
            {
                if (handleTransaction)
                {
                    await dbTransaction.RollbackAsync();
                }

                _logger.LogError("{Message}", ex.Message);
                throw;
            }
            finally
            {
                if (handleTransaction)
                {
                    dbTransaction.Dispose();
                }
            }
        }

        /// <inheritdoc/>
        public async Task CreateAnnouncementDocumentAsync(Guid announcementId, CreateAnnouncementDocumentDTO data)
        {
            // Checks and validations
            ArgumentNullException.ThrowIfNull(data, nameof(data));

            // Check for an existing db transaction and create one if not exists
            var dbTransaction = _announcementsRepository.GetCurrentTransaction();

            bool handleTransaction = false;

            if (dbTransaction == null)
            {
                dbTransaction = await _announcementsRepository.BeginTransactionAsync();
                handleTransaction = true;
            }

            try
            {
                // Check the announcement entity
                var announcement = await _announcementsRepository.CheckAnnouncementByIdAsync(announcementId);

                // Check the announcement status
                if (announcement.Status == AnnouncementStatus.Sent)
                {
                    throw new BadRequestException(
                        ApplicationErrors.INVALID_ANNOUNCEMENT_STATUS.ToErrorCode(),
                        $"The announcement with id '{announcement.Id}' is already sent.");
                }

                var documentId = await _documentManager.CreateDocumentAsync(
                    (int)data.Type,
                    string.IsNullOrEmpty(data.Description) ? data.File.Name : data.Description,
                    data.File,
                    "default",
                    true,
                    DateTime.UtcNow.AddHours(24));

                // Creates the annoucement document entity
                var announcementDocument = new AnnouncementDocument()
                {
                    AnnouncementId = announcementId,
                    DocumentId = documentId,
                };

                await _announcementDocumentsRepository.InsertAsync(announcementDocument, true);

                if (data.UploadComplete)
                {
                    // Check if the announcement needs to be sent now
                    if (announcement.SendAt <= DateTime.UtcNow)
                    {
                        // Send the announcement now
                        await SendAnnouncementAsync(announcementId, true);
                    }
                    else
                    {
                        // Set the annoucement status to Scheduled
                        announcement.Status = AnnouncementStatus.Scheduled;
                        await _announcementsRepository.UpdateAsync(announcement, true);
                    }
                }

                if (handleTransaction)
                {
                    await dbTransaction.CommitAsync();
                }
            }
            catch (Exception ex)
            {
                if (handleTransaction)
                {
                    await dbTransaction.RollbackAsync();
                }

                _logger.LogError("{Message}", ex.Message);
                throw;
            }
            finally
            {
                if (handleTransaction)
                {
                    dbTransaction.Dispose();
                }
            }
        }

        /// <inheritdoc/>
        public async Task DeleteAnnouncementDocumentAsync(Guid announcementDocumentId, bool uploadComplete)
        {
            // Check for an existing db transaction and create one if not exists
            var dbTransaction = _announcementsRepository.GetCurrentTransaction();

            bool handleTransaction = false;

            if (dbTransaction == null)
            {
                dbTransaction = await _announcementsRepository.BeginTransactionAsync();
                handleTransaction = true;
            }

            try
            {
                // Check the announcement and the announcemetn document entity
                var announcementDocument = await _announcementDocumentsRepository.CheckAnnouncementDocumentByIdAsync(announcementDocumentId);
                var announcement = await _announcementsRepository.CheckAnnouncementByIdAsync(announcementDocument.AnnouncementId);

                // Check the announcement status
                if (announcement.Status == AnnouncementStatus.Sent)
                {
                    throw new BadRequestException(
                        ApplicationErrors.INVALID_ANNOUNCEMENT_STATUS.ToErrorCode(),
                        $"The announcement with id '{announcement.Id}' is already sent. No documents can be deleted.");
                }

                // Delete the announcement document entity
                await _announcementDocumentsRepository.DeleteAsync(announcementDocument, true);

                // Delete the document
                await _documentManager.DeleteDocumentAsync(announcementDocument.DocumentId, true);

                if (uploadComplete)
                {
                    // Check if the announcement needs to be sent now
                    if (announcement.SendAt <= DateTime.UtcNow)
                    {
                        // Send the announcement now
                        await SendAnnouncementAsync(announcement.Id, true);
                    }
                    else
                    {
                        // Set the annoucement status to Scheduled
                        announcement.Status = AnnouncementStatus.Scheduled;
                        await _announcementsRepository.UpdateAsync(announcement, true);
                    }
                }
                else
                {
                    // Set the annoucement status to Scheduled
                    announcement.Status = AnnouncementStatus.Draft;
                    await _announcementsRepository.UpdateAsync(announcement, true);
                }

                if (handleTransaction)
                {
                    await dbTransaction.CommitAsync();
                }
            }
            catch (Exception ex)
            {
                if (handleTransaction)
                {
                    await dbTransaction.RollbackAsync();
                }

                _logger.LogError("{Message}", ex.Message);
                throw;
            }
            finally
            {
                if (handleTransaction)
                {
                    dbTransaction.Dispose();
                }
            }
        }

        /// <inheritdoc/>
        public async Task<AnnouncementDTO> GetAnnouncementByIdAsync(Guid announcementId)
        {
            // Check the announcement entity
            var announcementCheck = await _announcementsRepository.CheckAnnouncementByIdAsync(
                announcementId,
                options: o => o
                    .Include(a => a.Recipients)
                    .Include(a => a.Documents));

            var announcement = _mapper.Map<AnnouncementDTO>(announcementCheck);

            // Retrieve the documents
            foreach (var document in announcement.Documents)
            {
                document.Document = await _documentManager.GetDocumentAsync(document.DocumentId, true);
            }

            return announcement;
        }

        /// <inheritdoc/>
        public async Task SendAnnouncementAsync(Guid announcementId, bool saveChanges = false)
        {
            // Check for an existing db transaction and create one if not exists
            var dbTransaction = _announcementsRepository.GetCurrentTransaction();

            bool handleTransaction = false;

            if (dbTransaction == null)
            {
                dbTransaction = await _announcementsRepository.BeginTransactionAsync();
                handleTransaction = true;
            }

            try
            {
                // Check the announcement entity
                var announcement = await _announcementsRepository.CheckAnnouncementByIdAsync(
                announcementId,
                options: o => o.Include(a => a.Documents)
                    .Include(a => a.Recipients));

                // Check the status of the announcement to avoid sending an already sent announcement.
                if (announcement.Status == AnnouncementStatus.Sent)
                {
                    return;
                }

                // Create the inbox entities and documents for the master clients related
                // Create the unbox entity
                var inboxId = await _inboxService.CreateInboxItemAsync(
                    UserConsts.SystemUserId,
                    WellKnownRoleIds.System,
                    UserConsts.InboxUserId,
                    null,
                    announcement.Subject,
                    announcement.Body,
                    null,
                    saveChanges);

                // Create the inbox documents if any exists
                if (announcement.Documents.Count > 0)
                {
                    foreach (var document in announcement.Documents)
                    {
                        // Create the inbox Document entity
                        _inboxService.AppendDocumentToInboxId(inboxId, document.DocumentId);
                    }
                }

                // Create the inbox owners
                await CreateInboxOwnersAsync(inboxId, announcementId, saveChanges);

                // Send the email notfication
                await SendEmailToAnnouncementRecipientUsersAsync(announcementId, saveChanges);

                // Update the announcement entity
                announcement.Status = AnnouncementStatus.Sent;
                announcement.SentAt = DateTime.UtcNow;
                await _announcementsRepository.UpdateAsync(announcement);

                // Save changes using the bulk operation provider
                await _bulkOperationProvider.BulkSaveChangesAsync(_announcementRecipientsRepository.DbContext);

                if (handleTransaction)
                {
                    await dbTransaction.CommitAsync();
                }
            }
            catch (Exception ex)
            {
                if (handleTransaction)
                {
                    await dbTransaction.RollbackAsync();
                }

                _logger.LogError("{Message}", ex.Message);
                throw;
            }
            finally
            {
                if (handleTransaction)
                {
                    dbTransaction.Dispose();
                }
            }
        }

        /// <inheritdoc/>
        public async Task DeleteAnnouncementAsync(Guid announcementId)
        {
            // Check the announcement entity
            var announcement = await _announcementsRepository.CheckAnnouncementByIdAsync(
                announcementId,
                options: o => o.Include(a => a.Documents)
                    .Include(a => a.Recipients));

            // Delete the announcement recipients
            foreach (var recipient in announcement.Recipients)
            {
                await _announcementRecipientsRepository.DeleteAsync(recipient, false);
            }

            // Delete the announcement document
            foreach (var document in announcement.Documents)
            {
                await _announcementDocumentsRepository.DeleteAsync(document, false);
            }

            // Delete the announcement entity
            await _announcementsRepository.DeleteAsync(announcement, true);
        }

        #region Private methods

        /// <summary>
        /// Gets the predicate expression based on the provided filter criteria.
        /// </summary>
        /// <param name="filter">The filter criteria.</param>
        /// <returns>The predicate expression.</returns>
        private static Expression<Func<Announcement, bool>> GetAnnouncementPredicate(FilterAnnouncementsDTO filter)
        {
            ArgumentNullException.ThrowIfNull(filter, nameof(filter));

            Expression<Func<Announcement, bool>> predicate = s => true;

            // Filter by the subject or the body
            if (!string.IsNullOrEmpty(filter.GeneralSearchTerm))
            {
                predicate = predicate.And(a => a.Subject.Contains(filter.GeneralSearchTerm) || a.Body.Contains(filter.GeneralSearchTerm));
            }

            return predicate;
        }

        /// <summary>
        /// Apply the sorting configuration to the IQueryable.
        /// </summary>
        /// <param name="query">The IQUaryable of ListAnnouncementDTO.</param>
        /// <param name="sortingInfo">The specified sorting information.</param>
        /// <returns>The sorted IQueryable.</returns>
        private static IQueryable<ListAnnouncementDTO> ApplySorting(IQueryable<ListAnnouncementDTO> query, SortingInfo sortingInfo)
        {
            if (sortingInfo == null)
            {
                return query;
            }

            sortingInfo = sortingInfo.Validate();

            return query.SortBySpecification<ListAnnouncementDTO, FilterAnnouncementsDTO>(sortingInfo, a => a.SentAt);
        }

        /// <summary>
        /// Validate the data set before creating an announcement.
        /// </summary>
        /// <param name="data">The necessary data used to create announcements.</param>
        /// <exception cref="BadRequestException">When no target is selected.</exception>
        private async Task ValidateAnnouncementData(CreateUpdateAnnouncementDTO data)
        {
            // Check the announcement data
            Check.NotNullOrEmpty(data.Subject, nameof(data.Subject));
            Check.NotNullOrEmpty(data.Body, nameof(data.Body));

            // Chekc the announcement entity if exists
            if (data.Id.HasValue)
            {
                await _announcementsRepository.CheckAnnouncementByIdAsync(data.Id.Value);
            }

            if (!data.SendNow)
            {
                Check.NotDefaultOrNull(data.SendAt, nameof(data.SendAt));
            }

            if (data.SendToAllMasterClients)
            {
                // If this option is selected, a jurisdiction option must me choosen.
                if (data.SendToAllJurisdictions)
                {
                    return;
                }
                else
                {
                    await _jurisdictionsRepository.CheckJurisdictionByIdAsync(data.JurisdictionId);
                }

                return;
            }

            // Check for at least 1 item is selected to be announcement recipient
            if (data.MasterClientCodes.Count == 0 && data.LegalEntityIds.Count == 0 && data.UserIds.Count == 0)
            {
                throw new BadRequestException(
                    ApplicationErrors.ANNOUNCEMENT_RECIPIENTS_NOT_FOUND.ToErrorCode(),
                    $"Please specify at least one announcement recipient.");
            }

            // Check for maximum 10 master clients selected.
            if (data.MasterClientCodes.Count > 10)
            {
                throw new BadRequestException(
                    ApplicationErrors.ANNOUNCEMENT_RECIPIENTS_MAX_COUNT_EXCEEDED.ToErrorCode(),
                    $"Please selet a maximum of 10 master clients.");
            }

            // Check the master clients
            foreach (var masterClientCode in data.MasterClientCodes)
            {
                await _masterClientsRepository.CheckMasterClientByCodeAsync(masterClientCode);
            }

            // Check the legal entities recipients
            foreach (var legalEntityId in data.LegalEntityIds)
            {
                await _legalEntitiesRepository.CheckLegalEntityByIdAsync(legalEntityId);
            }

            // Check the users
            foreach (var userId in data.UserIds)
            {
                await _userRepository.CheckUserByIdAsync(userId);
            }
        }

        /// <summary>
        /// Configure the announcement recipients selected.
        /// </summary>
        /// <param name="announcementId">The announcement id as Guid.</param>
        /// <param name="data">The data used to create announcements.</param>
        private async Task ConfigureAnnouncementRecipientsAsync(Guid announcementId, CreateUpdateAnnouncementDTO data)
        {
            // Check the announcement entity
            await _announcementsRepository.CheckAnnouncementByIdAsync(                announcementId);

            // Check for an existing db transaction and create one if not exists
            var dbTransaction = _announcementsRepository.GetCurrentTransaction();

            bool handleTransaction = false;

            if (dbTransaction == null)
            {
                dbTransaction = await _announcementsRepository.BeginTransactionAsync();
                handleTransaction = true;
            }

            try
            {
                // Create announcement recipients
                // Check if the announcement must be sent to all master clients
                if (data.SendToAllMasterClients)
                {
                    // Check if the recipients are all jurisdictions
                    if (data.SendToAllJurisdictions)
                    {
                        var jurisdictions = await _jurisdictionsRepository.FindAllAsync();
                        var jurisdictionIds = jurisdictions.Select(x => x.Id).ToList();
                        await _securityManager.ValidateJurisdictionsAccess(jurisdictionIds);
                        await CreateAnnouncementRecipientsAsync(announcementId, nameof(Jurisdiction), jurisdictionIds);
                    }
                    else
                    {
                        await _securityManager.ValidateJurisdictionsAccess(new List<Guid> { data.JurisdictionId });

                        // Send the announcement to the selected jurisdiction
                        await CreateAnnouncementRecipientAsync(announcementId, nameof(Jurisdiction), data.JurisdictionId);
                    }
                }

                // Create master clients announcement recipients
                if (data.MasterClientCodes.Count > 0)
                {
                    var masterClientIds = (await _masterClientsRepository.FindByConditionAsync(mc => data.MasterClientCodes.Contains(mc.Code))).Select(mc => mc.Id).ToList();
                    await CreateAnnouncementRecipientsAsync(announcementId, nameof(MasterClient), masterClientIds);
                }

                // Create legal entity announcement recipients
                if (data.LegalEntityIds.Count > 0)
                {
                    await CreateAnnouncementRecipientsAsync(announcementId, nameof(LegalEntity), data.LegalEntityIds);
                }

                // Create user announcement recipients
                if (data.UserIds.Count > 0)
                {
                    await CreateAnnouncementRecipientsAsync(announcementId, nameof(ApplicationUser), data.UserIds);
                }

                if (handleTransaction)
                {
                    await dbTransaction.CommitAsync();
                }
            }
            catch (Exception ex)
            {
                if (handleTransaction)
                {
                    await dbTransaction.RollbackAsync();
                }

                _logger.LogError("{Message}", ex.Message);
                throw;
            }
            finally
            {
                if (handleTransaction)
                {
                    dbTransaction.Dispose();
                }
            }
        }

        /// <summary>
        /// Creates an announcement recipient.
        /// </summary>
        /// <param name="announcementId">The announcement id as Guid.</param>
        /// <param name="type">The recipient type as string.</param>
        /// <param name="recipientId">The recipient id as Guid.</param>
        private async Task CreateAnnouncementRecipientAsync(Guid announcementId, string type, Guid recipientId)
        {
            // Create the announcement recipient if not exist.
            var announcementRecipientCheck = await _announcementRecipientsRepository.FindFirstOrDefaultByConditionAsync(
                ao => ao.RecipientId == recipientId && ao.AnnouncementId == announcementId && ao.Type == type);

            if (announcementRecipientCheck == null)
            {
                var announcementRecipient = new AnnouncementRecipient()
                {
                    AnnouncementId = announcementId,
                    Type = type,
                    RecipientId = recipientId
                };

                await _announcementRecipientsRepository.InsertAsync(announcementRecipient, true);
            }
        }

        /// <summary>
        /// Creates announcement recipients.
        /// </summary>
        /// <param name="announcementId">The announcement id as Guid.</param>
        /// <param name="type">The recipient type as string.</param>
        /// <param name="recipientIds">The recipient ids as List of Guid.</param>
        private async Task CreateAnnouncementRecipientsAsync(Guid announcementId, string type, List<Guid> recipientIds)
        {
            // Create the announcement recipients if not exist.
            var annonuncementRecipients = new List<AnnouncementRecipient>();

            // Avoid the existing ones
            var existingAnnouncementRecipientIds = (await _announcementRecipientsRepository.FindByConditionAsync(
                ao => recipientIds.Contains(ao.RecipientId) && ao.AnnouncementId == announcementId && ao.Type == type)).Select(ar => ar.RecipientId).ToList();

            // Select the recipient ids that not exists
            var nonExistingRecipientIds = recipientIds.Where(i => !existingAnnouncementRecipientIds.Contains(i)).ToList();

            // Create the data set
            foreach (var recipientId in nonExistingRecipientIds)
            {
                annonuncementRecipients.Add(new AnnouncementRecipient(Guid.NewGuid())
                {
                    AnnouncementId = announcementId,
                    Type = type,
                    RecipientId = recipientId
                });
            }

            // Check for an existing db transaction and create one if not exists
            var dbTransaction = _announcementsRepository.GetCurrentTransaction();

            bool handleTransaction = false;

            if (dbTransaction == null)
            {
                dbTransaction = await _announcementsRepository.BeginTransactionAsync();
                handleTransaction = true;
            }

            try
            {
                // Create the announcement recipients using the bulk operation provider
                await _bulkOperationProvider.BulkInsertAsync(annonuncementRecipients, _announcementRecipientsRepository.DbContext);

                if (handleTransaction)
                {
                    await dbTransaction.CommitAsync();
                }
            }
            catch (Exception ex)
            {
                if (handleTransaction)
                {
                    await dbTransaction.RollbackAsync();
                }

                _logger.LogError("{Message}", ex.Message);
                throw;
            }
            finally
            {
                if (handleTransaction)
                {
                    dbTransaction.Dispose();
                }
            }
        }

        /// <summary>
        /// Create an inbox owner entity.
        /// </summary>
        /// <param name="inboxId">The inbox id as Guid.</param>
        /// <param name="announcementId">The announcement id as Guid.</param>
        /// <param name="saveChanges">Determine if the changes to the current dbContext are going to be saved or not.</param>
        private async Task CreateInboxOwnersAsync(Guid inboxId, Guid announcementId, bool saveChanges = false)
        {
            // Retrieve the configured announcement recipients
            var announcementRecipients = await _announcementRecipientsRepository.FindByConditionAsync(
                ao => ao.AnnouncementId == announcementId);

            // Create an inbox owner per announcement recipient
            foreach (var announcementRecipient in announcementRecipients)
            {
                var inboxOwner = new InboxOwner()
                {
                    InboxId = inboxId,
                    OwnerId = announcementRecipient.RecipientId,
                    Type = announcementRecipient.Type,
                };

                await _inboxOwnersRepository.InsertAsync(inboxOwner, saveChanges);
            }
        }

        /// <summary>
        /// Send an email notification to all announcement recipients.
        /// </summary>
        /// <param name="announcementId">The announcement id as Guid.</param>
        /// <param name="saveChanges">Determine if the changes to the current dbContext are going to be saved or not.</param>
        private async Task SendEmailToAnnouncementRecipientUsersAsync(Guid announcementId, bool saveChanges = false)
        {
            // Retrieve the announcement entity
            var announcement = await _announcementsRepository.CheckAnnouncementByIdAsync(announcementId);

            // Retrieve the announcement recipients for the announcement
            var announcementRecipients = (await _announcementRecipientsRepository.FindByConditionAsync(
                ao => ao.AnnouncementId == announcementId)).ToList();

            var userEmails = new List<string>();

            // Create a list of master client ids by using the announcement recipients
            var masterClientIds = new List<Guid>();
            foreach (var announcementRecipient in announcementRecipients)
            {
                // Retrieve the users for each master client, jurisdiction, legal entity and users
                if (announcementRecipient.Type == nameof(MasterClient))
                {
                    var masterClient = await _masterClientsRepository.CheckMasterClientByIdAsync(announcementRecipient.RecipientId);
                    masterClientIds.Add(masterClient.Id);
                }
                else if (announcementRecipient.Type == nameof(Jurisdiction))
                {
                    masterClientIds.AddRange(await RetrieveMasterClientIdsFromJurisdiction(announcementRecipient.RecipientId));
                }
                else if (announcementRecipient.Type == nameof(LegalEntity))
                {
                    masterClientIds.Add(await RetrieveMasterClientIdFromLegalEntity(announcementRecipient.RecipientId));
                }
            }

            // Retrieve the user emails with the selected master client ids
            userEmails.AddRange(await RetrieveUserEmailsFromMasterClients(masterClientIds));

            // Retrieve the recipients with the type ApplicationUser
            var applicationUserRecipients = announcementRecipients.Where(ar => ar.Type == nameof(ApplicationUser));
            foreach (var announcementRecipient in applicationUserRecipients)
            {
                var user = await _userRepository.CheckUserByIdAsync(announcementRecipient.RecipientId);
                userEmails.Add(user.Email);
            }

            // Send the email notification to each recipient
            foreach (var recipient in userEmails.Distinct())
            {
                await _communicationAppService.SendAnnouncementAsync(
                    recipient,
                    announcement.EmailSubject,
                    SystemNames.NewAnnouncementMessage,
                    saveChanges: saveChanges);
            }
        }

        /// <summary>
        /// Retrieve the recipients fron the selected master clients.
        /// </summary>
        /// <param name="masterClientIds">A list of the selecte master client ids as Guid.</param>
        /// <returns>A list of string containing the email addresses of the recipients.</returns>
        private async Task<List<string>> RetrieveUserEmailsFromMasterClients(List<Guid> masterClientIds)
        {
            // Retrieve the master client dataset
            var masterClients = await _masterClientsRepository.FindByConditionAsync(
                mc => masterClientIds.Contains(mc.Id),
                options: o => o.Include(mc => mc.MasterClientUsers)
                    .ThenInclude(mcu => mcu.User));

            var userEmails = masterClients.SelectMany(mc => mc.MasterClientUsers).Select(mcu => mcu.User.Email).Distinct();

            return userEmails.ToList();
        }

        /// <summary>
        /// Retrieve the master client ids from a selected jurisdiction.
        /// </summary>
        /// <remarks>
        /// This will retrieve the master client ids related to the selected jurisdiction.
        /// </remarks>
        /// <param name="jurisdictionId">The jurisdiction id as Guid.</param>
        /// <returns>A list of Guid containing the ids of the master clients.</returns>
        private async Task<List<Guid>> RetrieveMasterClientIdsFromJurisdiction(Guid jurisdictionId)
        {
            // Check the jurisdiction entity
            await _jurisdictionsRepository.CheckJurisdictionByIdAsync(jurisdictionId);

            // Retrieve the legal entities related to the jurisdiction
            var legalEntities = await _legalEntitiesRepository.FindByConditionAsync(le => le.JurisdictionId == jurisdictionId);

            return legalEntities.Select(le => le.MasterClientId).ToList();
        }

        /// <summary>
        /// Retrieve the master client related with the selected legal entity.
        /// </summary>
        /// <remarks>
        /// This will retrieve the master client related to the selected legal entity.
        /// </remarks>
        /// <param name="legalEntityId">The legal entity id as Guid.</param>
        /// <returns>The id of the master client as Guid.</returns>
        private async Task<Guid> RetrieveMasterClientIdFromLegalEntity(Guid legalEntityId)
        {
            // Retrieve the users related to the legal entity
            var legalEntity = await _legalEntitiesRepository.CheckLegalEntityByIdAsync(legalEntityId);

            return legalEntity.MasterClientId;
        }

        #endregion
    }

    /// <summary>
    /// Intermediate class to support Automapper Projection.
    /// This is needed because there is no EF navigation property to the recipients, as they can be of different types.
    /// So we first join the Announcement with the MasterClientCodes and JurisdictionNames and then map to the DTO.
    /// </summary>
    internal sealed class AnnouncementMappingIntermediateDTO
    {
        public Announcement Announcement { get; set; }
        public IList<string> MasterClientCodes { get; set; }
        public IList<string> JurisdictionNames { get; set; }
    }
}