// <copyright file="SearchBoDirRequestDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Application.Contracts.Shared;

namespace NetProGroup.Trust.Application.Contracts.BoDir
{
    /// <summary>
    /// DTO for searching Beneficial Owners and Directors.
    /// </summary>
    public class SearchBoDirRequestDTO : PagedAndSortedRequest
    {
        /// <summary>
        /// Gets or sets the Search Term filter.
        /// </summary>
        public string SearchTerm { get; set; }

        /// <summary>
        /// Gets or sets the Position filter (Director, BO).
        /// </summary>
        public BoDirPosition? Position { get; set; }

        /// <summary>
        /// Gets or sets the Production Office filter (THKO, TBVI, TCYP, TPANVG TNEV).
        /// </summary>
        public ProductionOfficeType? ProductionOffice { get; set; }

        /// <summary>
        /// Gets or sets the Confirmed Date range start.
        /// </summary>
        public DateTime? ConfirmedDateFrom { get; set; }

        /// <summary>
        /// Gets or sets the Confirmed Date range end.
        /// </summary>
        public DateTime? ConfirmedDateTo { get; set; }

        /// <summary>
        /// Gets or sets the Data Status filters.
        /// </summary>
        public List<BoDirDataStatus> DataStatuses { get; set; } = new List<BoDirDataStatus>();

        /// <summary>
        /// Gets or sets the BO/Dir specifics.
        /// </summary>
        public List<BoDirSpecifics> Specifics { get; set; }

        /// <summary>
        /// Gets or sets the field to sort by.
        /// </summary>
        [SortableColumns(
            nameof(BoDirItemDTO.LegalEntityName),
            nameof(BoDirItemDTO.Position),
            nameof(BoDirItemDTO.DirectorName),
            nameof(BoDirItemDTO.ProductionOffice),
            nameof(BoDirItemDTO.ReferralOffice),
            nameof(BoDirItemDTO.Specifics),
            nameof(BoDirItemDTO.Status),
            nameof(BoDirItemDTO.RequestUpdateDate),
            nameof(BoDirItemDTO.ConfirmedDate),
            nameof(BoDirItemDTO.VPEntityNumber),
            nameof(BoDirItemDTO.EntityPortalCode),
            nameof(BoDirItemDTO.MasterClientCode),
            nameof(BoDirItemDTO.DirectorVPCode),
            nameof(BoDirItemDTO.DirectorType),
            nameof(BoDirItemDTO.OfficerType),
            nameof(BoDirItemDTO.IsIndividual))]
        public override string SortBy { get; set; }
    }
}
