﻿// <copyright file="ContentTypeNotSupportedException.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Errors
{
    /// <summary>
    /// An exception to throw when the lock was not found.
    /// </summary>
    public class ContentTypeNotSupportedException : Exception
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="ContentTypeNotSupportedException"/> class.
        /// </summary>
        public ContentTypeNotSupportedException()
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ContentTypeNotSupportedException"/> class.
        /// </summary>
        /// <param name="message">The message for the exception.</param>
        public ContentTypeNotSupportedException(string message)
            : base(message)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ContentTypeNotSupportedException"/> class.
        /// </summary>
        /// <param name="messageFormat">The message format for the exception.</param>
        /// <param name="args">The formatting arguments.</param>
        public ContentTypeNotSupportedException(string messageFormat, params object[] args)
            : base(string.Format(messageFormat, args))
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ContentTypeNotSupportedException"/> class.
        /// </summary>
        /// <param name="message">The message for the exception.</param>
        /// <param name="innerException">The inner exception.</param>
        public ContentTypeNotSupportedException(string message, Exception innerException)
            : base(message, innerException)
        {
        }
    }
}