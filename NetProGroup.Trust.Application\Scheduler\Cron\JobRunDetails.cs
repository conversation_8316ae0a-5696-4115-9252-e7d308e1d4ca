﻿// <copyright file="JobRunDetails.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Scheduler.Cron
{
    /// <summary>
    /// Job run details model.
    /// </summary>
    public class JobRunDetails
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="JobRunDetails"/> class.
        /// </summary>
        /// <param name="jobId">Id of the scheduled job.</param>
        /// <param name="implementationType">Job implementation type.</param>
        /// <param name="lastRun">Datetiem when the job last ran.</param>
        /// <param name="cronExpression">The CRON expression to use for this job.</param>
        public JobRunDetails(Guid jobId, Type implementationType, DateTime? lastRun, string cronExpression)
        {
            LastRunUtc = lastRun.GetValueOrDefault(DateTime.UtcNow);
            JobId = jobId;
            CronExpression = cronExpression;
            ImplementationType = implementationType;
        }

        /// <summary>
        /// Gets or sets the id of the job in the database.
        /// </summary>
        public Guid JobId { get; set; }

        /// <summary>
        /// Gets or sets the date and time that the jub last ran.
        /// </summary>
        public DateTime LastRunUtc { get; set; }

        /// <summary>
        /// Gets or sets the optional code for the jurisdiction to sync.
        /// </summary>
        public string JurisdictionCode { get; set; }

        /// <summary>
        /// Gets or sets the cron expression for the job.
        /// </summary>
        public string CronExpression { get; set; }

        /// <summary>
        /// Gets or sets the type of the job implementation.
        /// </summary>
        public Type ImplementationType { get; set; }
    }
}
