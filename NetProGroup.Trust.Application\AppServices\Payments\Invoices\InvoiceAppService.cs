// <copyright file="InvoiceAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.Exceptions;
using NetProGroup.Trust.Application.Contracts.Errors;
using NetProGroup.Trust.Application.Contracts.Payments.Invoices;
using NetProGroup.Trust.DataManager.Payments.Invoices;
using NetProGroup.Trust.DataManager.Security;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.Domain.Shared.Modules;
using NetProGroup.Trust.Domain.Shared.Permissions;
using X.PagedList;

namespace NetProGroup.Trust.Application.AppServices.Payments.Invoices
{
    /// <summary>
    /// Service for managing invoices and providing invoice-related operations.
    /// Implements <see cref="IInvoiceAppService"/>.
    /// </summary>
    public class InvoiceAppService : IInvoiceAppService
    {
        private readonly IInvoiceDataManager _dataManager;
        private readonly ISecurityManager _securityManager;

        /// <summary>
        /// Initializes a new instance of the <see cref="InvoiceAppService"/> class.
        /// </summary>
        /// <param name="dataManager">An instance of <see cref="IInvoiceDataManager"/> for managing invoice data.</param>
        /// <param name="securityManager">An instance of <see cref="ISecurityManager"/> for managing security operations.</param>
        public InvoiceAppService(IInvoiceDataManager dataManager, ISecurityManager securityManager)
        {
            _dataManager = dataManager;
            _securityManager = securityManager;
        }

        /// <summary>
        /// Retrieves a paged list of invoices based on the provided request parameters.
        /// </summary>
        /// <param name="request">The request containing filters and pagination parameters.</param>
        /// <returns>A task that represents the asynchronous operation.
        /// The task result contains a paged list of invoices.</returns>
        public async Task<IPagedList<InvoiceDTO>> ListInvoicesAsync(InvoiceListRequestDTO request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            await _securityManager.RequireClientUserAsync();

            request.UserId = _securityManager.UserId;

            return await _dataManager.ListInvoicesAsync(request);
        }

        /// <summary>
        /// Retrieves an invoice by its unique identifier.
        /// </summary>
        /// <param name="id">The unique identifier of the invoice to retrieve.</param>
        /// <returns>A task that represents the asynchronous operation.
        /// The task result contains the invoice details.</returns>
        public async Task<InvoiceDTO> GetInvoiceByIdAsync(Guid id)
        {
            if (!await _securityManager.UserIsClient())
            {
                var modules = await _dataManager.GetModulesForInvoiceIdAsync(id);

                var permissions = modules.Select(m => m.Key switch
                {
                    ModuleKeyConsts.SimplifiedTaxReturn => WellKnownPermissionNames.STRModule_Submissions_View,

                    // ModuleKeyConsts.BasicFinancialReport => WellKnownPermissionNames.BFRModule_Invoices_Export,
                    _ => throw new PreconditionFailedException(ApplicationErrors.NOT_SUPPORTED_FOR_MODULE.ToErrorCode(), $"{nameof(GetInvoiceByIdAsync)} not supported for module '{modules}'")
                }).ToArray();

                await _securityManager.RequireOneOfPermissionsAsync(permissions);
            }
            else
            {
                var masterClient = await _dataManager.GetMasterClientByInvoiceId(id);
                await _securityManager.RequireClientAccessToMasterClientAsync(masterClient.Id);
            }

            return await _dataManager.GetInvoiceAsync(id);
        }
    }
}
