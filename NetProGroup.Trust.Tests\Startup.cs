﻿// <copyright file="Startup.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper.Extensions.ExpressionMapping;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Framework.Azure.MSGraph;
using NetProGroup.Framework.Caching;
using NetProGroup.Framework.Services.Extensions;
using NetProGroup.Trust.Application.Seeders;
using NetProGroup.Trust.DataManager.AutoMapper;
using NetProGroup.Trust.DataManager.Configurations;
using NetProGroup.Trust.Domain.Repository;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Template;

namespace NetProGroup.Trust.Tests
{
    /// <summary>
    /// Startup class for the tests.
    /// </summary>
    internal class Startup
    {
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            var connectionString = Configuration.GetConnectionString("Default");

            //services.AddSingleton<AuditableEntitiesInterceptor>();

            //DbContextOptionsBuilder bldr;
            string databaseName = Guid.NewGuid().ToString();
            services.AddDbContext<TrustDbContext>(
                (sp, options) =>
                {
                    //var interceptor = sp.GetRequiredService<AuditableEntitiesInterceptor>();

                    options.UseInMemoryDatabase(databaseName: databaseName);

                    options.ConfigureWarnings(x =>
                        x.Ignore(InMemoryEventId.TransactionIgnoredWarning)
                    );
                }
            );

            services.Configure<TrustOfficeOptions>(Configuration.GetSection(TrustOfficeOptions.TrustOffice));

            services.AddTransient(typeof(IExcelTemplateService<>), typeof(ExcelTemplateService<>));

            // Messaging
            services.AddNetProMessaging<TrustDbContext>(options =>
            {
                options.SendGridOptions.APIKey = Configuration.GetValue<string>("SendGrid:ApiKey");
                options.EmailDefaultOptions.FromEmail = Configuration.GetValue<string>(
                    "Email:FromEmail"
                );
                options.EmailDefaultOptions.FromName = Configuration.GetValue<string>(
                    "Email:FromName"
                );
            });

            // Configurations
            services.AddNetProConfiguration<TrustDbContext>();

            // Documents
            //var storageAccountname = "tcifirearms";
            //var storageSharedKey = "pXoVqiZmoX0xaCCJ3OCUhfXmQO3/3OR7/CFAyaiFKTuuwB3FC7LxPjMF+SKLzLEHYcn2OByXdZdz+AStPAYwqA==";
            //var storageContainername = "foodhandler-documents";

            services.AddNetProDocuments<TrustDbContext>(options =>
            {
                //options.StorageAccountOptions.AddStorageAccount(storageAccountname, storageSharedKey, storageContainername);
            });

            services.AddNetProConfiguration<TrustDbContext>();

            // Auditing
            services.AddNetProAuditing<TrustDbContext>();

            // Activity logs
            services.AddNetProActivityLogs<TrustDbContext>();

            // Documents
            var storageAccountname = Configuration.GetValue<string>("BlobStorage:AccountName", "none");
            var storageContainername = Configuration.GetValue<string>("BlobStorage:ContainerName", "documents");
            var storageSharedKey = Configuration.GetValue<string>("BlobStorage:SharedKey", "");

            services.AddNetProDocuments<TrustDbContext>(options =>
            {
                options.StorageAccountOptions.AddStorageAccount("default", storageAccountname, storageSharedKey, storageContainername);
            });

            // Countries
            services.AddNetProCountries<TrustDbContext>(options =>
            {
                options.SeedDatabase = true;
            });

            // Identity
            services.AddNetProIdentity<TrustDbContext>();

            // Locking
            services.AddNetProLocks<TrustDbContext>();

            // MSGraph
            services.AddMSGraphAD(options => { });
            services.AddMSGraphB2C(options => { });

            // Caching
            services.AddScoped<ICacheManager, PerRequestCacheManager>();

            services.AddAutoMapper(
               cfg =>
               {
                   cfg.AddExpressionMapping();
               },
               // Scan for automapper profiles in this assembly
               typeof(ApplicationProfile).Assembly
           );

            TrustDbContext.Seed(
                (b) =>
                {
                    b.SeedJurisdictions();
                    b.SeedCurrencies();
                    b.SeedRoles();
                    b.SeedUsers();
                    b.SeedUserRoles();
                    b.SeedMessageTemplates();
                    b.SeedConfigurations();
                }
            );
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env) { }
    }
}
