﻿// <copyright file="InboxDataManager.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Framework.Extensions;
using NetProGroup.Framework.Mvc.Contexts;
using NetProGroup.Framework.Services.Communication.EFRepository;
using NetProGroup.Framework.Services.Identity.EFModels;
using NetProGroup.Framework.Services.Identity.Repository;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.Application.Contracts.Errors;
using NetProGroup.Trust.Application.Contracts.Inboxes;
using NetProGroup.Trust.DataManager.Auditing;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.DataManager.InboxItems;
using NetProGroup.Trust.DataManager.MasterClients;
using NetProGroup.Trust.DataManager.MasterClients.RequestResponses;
using NetProGroup.Trust.Domain.Inboxes;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Domain.Repository.Extensions;
using NetProGroup.Trust.Domain.Shared.Consts;
using X.PagedList;

namespace NetProGroup.Trust.DataManager.Messaging
{
    /// <summary>
    /// The datamanager for inboxes.
    /// </summary>
    public class InboxDataManager : IInboxDataManager
    {
        private readonly IInboxRepository _inboxRepository;
        private readonly IInboxReadStatusesRepository _inboxReadStatusesRepository;
        private readonly IInboxOwnersRepository _inboxOwnersRepository;
        private readonly IJurisdictionsRepository _jurisdictionsRepository;
        private readonly ILegalEntitiesRepository _legalEntitiesRepository;
        private readonly IMasterClientsDataManager _masterClientsDataManager;
        private readonly IMasterClientsRepository _masterClientsRepository;
        private readonly ISystemAuditManager _systemAuditManager;
        private readonly IUserRepository _usersRepository;
        private readonly IWorkContext _workContext;

        /// <summary>
        /// Initializes a new instance of the <see cref="InboxDataManager"/> class.
        /// </summary>
        /// <param name="inboxRepository">The inbox repository.</param>
        /// <param name="inboxReadStatusesRepository">The inbox read statuses repository.</param>
        /// <param name="inboxOwnersRepository">The inbox owners repository.</param>
        /// <param name="jurisdictionsRepository">The repository for jurisdictions.</param>
        /// <param name="legalEntitiesRepository">The repository for legal entities.</param>
        /// <param name="masterClientsDataManager">The master clients data manager.</param>
        /// <param name="masterClientsRepository">The repository for master clients.</param>
        /// <param name="systemAuditManager">The system audit manager.</param>
        /// <param name="usersRepository">The repository for users.</param>
        /// <param name="workContext">The workContext service.</param>
        public InboxDataManager(
            IInboxRepository inboxRepository,
            IInboxReadStatusesRepository inboxReadStatusesRepository,
            IInboxOwnersRepository inboxOwnersRepository,
            IJurisdictionsRepository jurisdictionsRepository,
            ILegalEntitiesRepository legalEntitiesRepository,
            IMasterClientsDataManager masterClientsDataManager,
            IMasterClientsRepository masterClientsRepository,
            ISystemAuditManager systemAuditManager,
            IUserRepository usersRepository,
            IWorkContext workContext)
        {
            _inboxRepository = inboxRepository;
            _inboxReadStatusesRepository = inboxReadStatusesRepository;
            _inboxOwnersRepository = inboxOwnersRepository;
            _jurisdictionsRepository = jurisdictionsRepository;
            _legalEntitiesRepository = legalEntitiesRepository;
            _masterClientsDataManager = masterClientsDataManager;
            _masterClientsRepository = masterClientsRepository;
            _systemAuditManager = systemAuditManager;
            _usersRepository = usersRepository;
            _workContext = workContext;
        }

        /// <inheritdoc/>
        public async Task<InboxInfoDTO> GetInboxInfoAsync(Guid userId)
        {
            Check.NotDefaultOrNull<Guid>(userId, nameof(userId));

            var result = new InboxInfoDTO();

            var ownerIds = await RetrieveOwnerIdsByUserId(userId);

            // Retrieve all the inboxOwner entities created for the authenticated user.
            var inboxOwnerEntries = await _inboxOwnersRepository.FindByConditionAsync(
                io => ownerIds.Contains(io.OwnerId));

            // Select the distinct inbox entities
            var inboxIds = inboxOwnerEntries.Select(io => io.InboxId).Distinct().ToList();

            // Retrieve the inboxReadStatus for every inbox record if exists.
            var inboxReadStatuses = new List<InboxReadStatus>();

            foreach (var inboxId in inboxIds)
            {
                var inboxReadStatus = await _inboxReadStatusesRepository.FindFirstOrDefaultByConditionAsync(
                    irs => irs.InboxId == inboxId);

                if (inboxReadStatus != null)
                {
                    inboxReadStatuses.Add(inboxReadStatus);
                }
            }

            result.TotalMessages = inboxIds.Count;
            result.TotalUnreadMessages = inboxIds.Count - inboxReadStatuses.Count;

            return result;
        }

        /// <inheritdoc/>
        public async Task<IPagedList<InboxMessageListItemDTO>> GetInboxMessagesAsync(Guid userId, bool? isRead = null, int pageNumber = 1, int pageSize = int.MaxValue)
        {
            Check.NotDefaultOrNull<Guid>(userId, nameof(userId));

            // Retrieve the Owner ids for the authenticated user.
            var ownerIds = await RetrieveOwnerIdsByUserId(userId);

            // Retrieve all the inboxOwner entities created for the authenticated user.
            var inboxOwnerEntries = await _inboxOwnersRepository.FindByConditionAsync(
                io => ownerIds.Contains(io.OwnerId));

            // Select the distinct inbox entities
            var inboxIds = inboxOwnerEntries.Select(io => io.InboxId).Distinct().ToList();

            // Retrieve the inboxes from the InboxOwner table
            var pagedItems = await _inboxRepository.FindByConditionAsPagedListAsync(
                i => inboxIds.Contains(i.Id),
                options: o =>
                    o.Include(i => i.FromUser)
                    .Include(x => x.ToUser)
                    .Include(x => x.Documents)
                    .OrderByDescending(x => x.CreatedAt),
                pageNumber: pageNumber,
                pageSize: pageSize);

            var subItems = pagedItems.Select(i =>
                new InboxMessageListItemDTO
                {
                    Id = i.Id,
                    Subject = i.Subject,
                    CreatedAt = i.CreatedAt,
                    HasAttachments = i.Documents.Count > 0,
                    FromUserId = i.FromUserId.Equals(UserConsts.SystemUserId) ? null : i.FromUserId,
                    FromUserName = i.FromUserId.Equals(UserConsts.SystemUserId) ? i.FromUser.Name : i.FromUser.GetDisplayName(),
                    ToUserId = i.ToUserId,
                    ToUserName = i.ToUser.GetDisplayName(),
                });

            // Retrieve the collection for inbox read status
            var inboxReadStatusElements = await _inboxReadStatusesRepository.FindByConditionAsync(
                    irs => irs.UserId == userId && inboxIds.Contains(irs.InboxId));

            // Retrieve the collection for inbox owners
            var inboxOwnerElements = await _inboxOwnersRepository.FindByConditionAsync(
                    io => inboxIds.Contains(io.InboxId));

            foreach (var item in subItems)
            {
                // Check if the inbox has been read
                var inboxReadStatus = inboxReadStatusElements.FirstOrDefault(irs => irs.InboxId == item.Id);

                if (inboxReadStatus != null)
                {
                    item.IsRead = true;
                    item.ReadAt = inboxReadStatus.ReadAt;
                }

                var inboxOwners = inboxOwnerElements.Where(irs => irs.InboxId == item.Id);

                await SetInboxOwnerDetailsAsync(inboxOwners.ToList(), item);
            }

            return new StaticPagedList<InboxMessageListItemDTO>(subItems, pagedItems.GetMetaData());
        }

        /// <inheritdoc/>
        public async Task<InboxMessageDTO> GetInboxMessageAsync(Guid messageId)
        {
            Check.NotDefaultOrNull<Guid>(messageId, nameof(messageId));

            var message = await _inboxRepository.CheckInboxMessageAsync(messageId);

            // Check if the authenticated user is an owner to the inbox entity
            // Retrieve the Owner ids for the authenticated user.
            var ownerIds = await RetrieveOwnerIdsByUserId(_workContext.IdentityUserId.Value);

            var inboxOwnerCheck = await _inboxOwnersRepository.FindFirstOrDefaultByConditionAsync(
                io => io.InboxId == messageId && ownerIds.Contains(io.OwnerId));

            if (inboxOwnerCheck == null)
            {
                throw new ForbiddenException(
                        ApplicationErrors.INBOX_OWNER_NOT_FOUND.ToErrorCode(),
                        $"The user with id '{_workContext.IdentityUserId.Value}' does not have acces to the message with id '{messageId}'.");
            }

            message = await _inboxRepository.GetByIdAsync(
                messageId,
                options: (q) =>
                {
                    return q.Include(x => x.ToUser)
                            .Include(x => x.FromUser)
                            .Include(x => x.Documents).ThenInclude(x => x.Document);
                });

            // Check for the master client inbox owners
            var inboxOwnerEntries = await _inboxOwnersRepository.FindByConditionAsync(
                io => io.InboxId == messageId);

            // Read status for the authenticated user
            var readStatus = await _inboxReadStatusesRepository.FindSingleOrDefaultByConditionAsync(
                irs => irs.InboxId == messageId && irs.UserId == _workContext.IdentityUserId.Value);

            var result = new InboxMessageDTO
            {
                Id = message.Id,
                Subject = message.Subject,
                CreatedAt = message.CreatedAt,
                IsRead = readStatus != null,
                ReadAt = readStatus != null ? readStatus.ReadAt : null,
                HasAttachments = message.Documents.Count > 0,
                FromUserId = message.FromUserId.Equals(UserConsts.SystemUserId) ? null : message.FromUserId,
                FromUserName = message.FromUserId.Equals(UserConsts.SystemUserId) ? null : message.FromUser.GetDisplayName(),
                ToUserId = message.ToUserId,
                ToUserName = message.ToUser.GetDisplayName(),

                Body = message.Body
            };

            await SetInboxOwnerDetailsAsync(inboxOwnerEntries.ToList(), result);

            foreach (var document in message.Documents)
            {
                result.InboxAttachments.Add(new InboxAttachmentDTO
                {
                    Id = document.DocumentId,
                    Filename = document.Document.Filename,
                    Description = document.Document.Description,
                    FileSize = document.Document.FileSize,
                });
            }

            return result;
        }

        /// <inheritdoc />
        public async Task CreateInboxReadStatusIfNotExistsAsync(Guid inboxId, Guid userId, DateTime readAt)
        {
            Check.NotDefaultOrNull<Guid>(inboxId, nameof(inboxId));
            Check.NotDefaultOrNull<Guid>(userId, nameof(userId));
            Check.NotDefaultOrNull<DateTime>(readAt, nameof(readAt));

            await CheckUserHasAccessToInbox(inboxId, userId);

            var status = await GetInboxReadStatusForUserAsync(inboxId, userId);
            if (!status.Any())
            {
                // Create new status
                var newStatus = new InboxReadStatus
                {
                    InboxId = inboxId,
                    UserId = userId,
                    ReadAt = readAt,
                };

                await _inboxReadStatusesRepository.InsertAsync(newStatus, true);
            }
        }

        /// <inheritdoc />
        public async Task DeleteInboxReadStatusIfExistsAsync(Guid inboxId, Guid userId)
        {
            Check.NotDefaultOrNull<Guid>(inboxId, nameof(inboxId));
            Check.NotDefaultOrNull<Guid>(userId, nameof(userId));

            var status = await GetInboxReadStatusForUserAsync(inboxId, userId);

            if (status != null)
            {
                await _inboxReadStatusesRepository.DeleteAsync(status, true);
            }
        }

        /// <summary>
        /// Sets the inbox owner configuration for a given inbox entity.
        /// </summary>
        /// <param name="inboxOwnerEntries">The inbox owner entities for the inbox.</param>
        /// <param name="inboxMessage">The inbox message as InboxMessageListItemDTO.</param>
        private async Task SetInboxOwnerDetailsAsync(List<InboxOwner> inboxOwnerEntries, InboxMessageListItemDTO inboxMessage)
        {
            // Retrieve datasets fro Master clients, jurisdictions, legal entities and users to prevent multiple queries.
            var ownerIds = inboxOwnerEntries.Select(x => x.OwnerId).ToList();
            var masterClients = await _masterClientsRepository.FindByConditionAsync(mc => ownerIds.Contains(mc.Id));
            var jurisdictions = await _jurisdictionsRepository.FindByConditionAsync(j => ownerIds.Contains(j.Id));
            var legalEntities = await _legalEntitiesRepository.FindByConditionAsync(le => ownerIds.Contains(le.Id));
            var users = await _usersRepository.GetListAsync(ownerIds);

            foreach (var inboxOwner in inboxOwnerEntries)
            {
                switch (inboxOwner.Type)
                {
                    case nameof(MasterClient):
                        var masterClient = masterClients.First(mc => mc.Id == inboxOwner.OwnerId);
                        inboxMessage.MasterClients.Add(masterClient.Code);
                        break;

                    case nameof(Jurisdiction):
                        var jurisdiction = jurisdictions.First(j => j.Id == inboxOwner.OwnerId);
                        inboxMessage.Jurisdictions.Add(jurisdiction.Name);
                        break;

                    case nameof(LegalEntity):
                        var legalEntity = legalEntities.First(le => le.Id == inboxOwner.OwnerId);
                        inboxMessage.LegalEntities.Add(legalEntity.Name);
                        break;

                    case nameof(ApplicationUser):
                        var user = users.First(u => u.Id == inboxOwner.OwnerId);
                        inboxMessage.Users.Add(user.GetDisplayName());
                        break;

                    default:
                        throw new BadRequestException(
                            ApplicationErrors.INVALID_INBOX_OWNER.ToErrorCode(),
                            $"The owner with id '{inboxOwner.OwnerId}' and type '{inboxOwner.Type}' is not valid.");
                }
            }
        }

        /// <summary>
        /// Retreives a list of ownerIds containing the jurisidictions, masterlcients, legal entities related to a user given itd Id.
        /// </summary>
        /// <param name="userId">The ApplicationUser id as Guid.</param>
        /// <returns>A list of Ids as Guid with the information related to the given user.</returns>
        private async Task<List<Guid>> RetrieveOwnerIdsByUserId(Guid userId)
        {
            // Retrieve master clients, jurisdictions and legal entities related to the authenticated user and populate a list of owner ids.
            var ownerIds = new List<Guid>();

            var userMasterClients = await _masterClientsDataManager.SearchMasterClientsAsync(
                new SearchMasterClientsRequest()
                {
                    UserId = userId,
                    IncludeLegalEntities = true
                });

            // Add the master clients ids.
            ownerIds.AddRange(userMasterClients.MasterClientItems.Select(mc => mc.MasterClientId));

            // Add the legal entity ids.
            ownerIds.AddRange(userMasterClients.MasterClientItems.SelectMany(mc => mc.Companies).Select(c => c.Id));

            // Add the jurisdiction ids.
            ownerIds.AddRange(userMasterClients.MasterClientItems.SelectMany(mc => mc.Companies).Select(c => c.JurisdictionId));

            // Add the autheticated user id
            ownerIds.Add(userId);

            return ownerIds.Distinct().ToList();
        }

        /// <summary>
        /// Get all inbox owner records for a user.
        /// </summary>
        /// <param name="userId">The user ID.</param>
        /// <param name="inboxId">The inbox ID.</param>
        /// <returns>The list of inbox owners.</returns>
        private async Task<IEnumerable<InboxOwner>> GetInboxOwnersForUserIdAndInboxIdAsync(Guid userId, Guid inboxId)
        {
            var masterClientsResponse = await _masterClientsDataManager.SearchMasterClientsAsync(
                new SearchMasterClientsRequest()
                {
                    UserId = userId,
                    IncludeLegalEntities = true
                });
            var masterClients = masterClientsResponse.MasterClientItems;

            var masterClientIDs = masterClients
                .Select(masterClient => masterClient.MasterClientId)
                .ToList();
            var companyIDs = masterClients
                .SelectMany(masterClient => masterClient.Companies)
                .Select(company => company.Id)
                .ToList();
            var jurisdictionIDs = masterClients
                .SelectMany(masterClient => masterClient.Companies)
                .Select(company => company.JurisdictionId)
                .Distinct()
                .ToList();

            var inboxOwners = await _inboxOwnersRepository.FindByConditionAsync(o =>
                o.InboxId == inboxId &&
                (
                    (o.Type == nameof(ApplicationUser) && o.OwnerId == userId) ||
                    (o.Type == nameof(LegalEntity) && companyIDs.Contains(o.OwnerId)) ||
                    (o.Type == nameof(MasterClient) && masterClientIDs.Contains(o.OwnerId)) ||
                    (o.Type == nameof(Jurisdiction) && jurisdictionIDs.Contains(o.OwnerId))));

            return inboxOwners;
        }

        private Task<IEnumerable<InboxReadStatus>> GetInboxReadStatusForUserAsync(Guid inboxId, Guid userId)
        {
            return _inboxReadStatusesRepository.FindByConditionAsync(s => s.InboxId == inboxId && s.UserId == userId);
        }

        private async Task CheckUserHasAccessToInbox(Guid inboxId, Guid userId)
        {
            var owners = await GetInboxOwnersForUserIdAndInboxIdAsync(userId, inboxId);

            if (!owners.Any())
            {
                throw new ForbiddenException(ApplicationErrors.INVALID_INBOX_OWNER.ToErrorCode(), "User has no access to this inbox.");
            }
        }
    }
}