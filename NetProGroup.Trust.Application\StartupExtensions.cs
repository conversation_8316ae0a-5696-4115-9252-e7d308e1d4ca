﻿// <copyright file="StartupExtensions.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper.Extensions.ExpressionMapping;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Identity.Web;
using NetProGroup.Trust.Domain.Repository;
using NetProGroup.Framework.Azure.MSGraph;
using NetProGroup.Framework.DependencyInjection.Extensions;
using NetProGroup.Framework.Services.Extensions;
using Serilog;
using Asp.Versioning;
using Microsoft.ApplicationInsights.Extensibility;
using NetProGroup.Framework.Caching;
using NetProGroup.Trust.Application.Scheduler.Cron;
using NetProGroup.Trust.Payment.Provider;
using NetProGroup.Trust.Application.BackgroundServices;
using Microsoft.EntityFrameworkCore.Diagnostics;
using NetProGroup.Trust.DataManager.Configurations;
using NetProGroup.Trust.DataManager;
using NetProGroup.Trust.Domain.Shared.Enums;
using Hangfire;
using Microsoft.Extensions.Logging;
using NetProGroup.Framework.SendGrid;
using NetProGroup.Trust.Application.Common;
using NetProGroup.Trust.Application.Seeders;
using NetProGroup.Trust.DataMigration;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Template;
using System.Reflection;
using NetProGroup.Framework.Services.Locks.EFRepository;
using NetProGroup.Framework.Smtp;
using NetProGroup.Trust.Application.Communication;
using NetProGroup.Trust.Application.Contracts.Common;
using NetProGroup.Trust.Domain.Repository.Submissions;
using NetProGroup.Trust.Domain.Submissions;

namespace NetProGroup.Trust.Application
{
    /// <summary>
    /// Extensions for the startup of the API.
    /// </summary>
    public static class StartupExtensions
    {
        private static string[] _databaseTags = new string[] { "db" };

        /// <summary>
        /// Setup health checks.
        /// </summary>
        /// <param name="builder">The application builder instance.</param>
        public static void SetupHealthChecks(this WebApplicationBuilder builder)
        {
            ArgumentNullException.ThrowIfNull(builder, nameof(builder));

            builder.Services.AddHealthChecks()
                .AddDbContextCheck<TrustDbContext>(tags: _databaseTags);
        }

        /// <summary>
        /// Configures the use of the database.
        /// </summary>
        /// <param name="builder">The application builder instance.</param>
        public static void SetupDbContext(this WebApplicationBuilder builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            // Setup DbContext
            var connectionString = builder.Configuration.GetConnectionString("Default");

            builder.Services.AddDbContext<TrustDbContext>(
                (options) =>
                {
                    options.UseSqlServer(
                        connectionString,
                        _ =>
                        {
                            // o.MigrationsAssembly("NetProGroup.Trust.Domain.Repository");
                            // o.UseQuerySplittingBehavior(QuerySplittingBehavior.SplitQuery);
                        });

                    options.ConfigureWarnings(w => w.Ignore(SqlServerEventId.SavepointsDisabledBecauseOfMARS));
                });

            // Seeds the data for migrations
            TrustDbContext.Seed(
                (b) =>
                {
                    b.SeedJurisdictions();
                    b.SeedCurrencies();
                    b.SeedRoles();
                    b.SeedUsers();
                    b.SeedUserRoles();
                    b.SeedMessageTemplates();
                    b.SeedConfigurations();
                });
        }

        /// <summary>
        /// Performs migration of the database.
        /// </summary>
        /// <param name="services">Instance of a service provider.</param>
        public static void Migrate(this IServiceProvider services)
        {
            using (var serviceScope = services.GetRequiredService<IServiceScopeFactory>().CreateScope())
            {
                var context = serviceScope.ServiceProvider.GetRequiredService<TrustDbContext>();
                context.Database.Migrate();
            }
        }

        /// <summary>
        /// Configures the NetPro framework.
        /// </summary>
        /// <param name="builder">The application builder instance.</param>
        public static void ConfigureNetProFramework(this WebApplicationBuilder builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            // Logging
            builder.Services.AddNetProLoggingTemp();
            builder.Host.UseNetProLogging();

            // Locking
            builder.Services.AddNetProLocks<TrustDbContext>();

            // Messaging
            builder.Services.AddNetProMessaging<TrustDbContext>(options =>
            {
                options.EmailDefaultOptions.FromEmail = builder.Configuration.GetValue<string>("Email:FromEmail");
                options.EmailDefaultOptions.FromName = builder.Configuration.GetValue<string>("Email:FromName");

                options.SendGridOptions.APIKey = builder.Configuration.GetValue<string>("SendGrid:ApiKey");

                if (string.IsNullOrEmpty(builder.Configuration["Smtp:Host"]))
                {
                    options.EmailProtocol = Framework.Services.Communication.EmailAndSMS.Services.EmailProtocol.Sendgrid;
                }
                else
                {
                    options.EmailProtocol = Framework.Services.Communication.EmailAndSMS.Services.EmailProtocol.Smtp;
                }

                options.EmailDefaultOptions.EmbeddedImages.Add("logo", new Framework.Services.Communication.Configuration.EmailAttachment
                {
                    Filename = "logo.jpg",
                    AttachmentData = GetResource("trident_logo.jpg")
                });
            });

            // Configurations
            builder.Services.AddNetProConfiguration<TrustDbContext>();

            // Auditing
            builder.Services.AddNetProAuditing<TrustDbContext>();

            // Activity logs
            builder.Services.AddNetProActivityLogs<TrustDbContext>();

            // Documents
            var storageAccountname = builder.Configuration.GetValue("BlobStorage:AccountName", "none");
            var storageContainername = builder.Configuration.GetValue("BlobStorage:ContainerName", "documents");
            var storageSharedKey = builder.Configuration.GetValue("BlobStorage:SharedKey", "");

            builder.Services.AddNetProDocuments<TrustDbContext>(options =>
            {
                options.StorageAccountOptions.AddStorageAccount("default", storageAccountname, storageSharedKey, storageContainername);
            });

            //// Countries
            // builder.Services.AddNetProCountries<ProjectsDashboardDbContext>(options =>
            // {
            //    options.SeedDatabase = true;
            // });

            // Identity
            builder.Services.AddNetProIdentity<TrustDbContext>();

            // Payments
            // builder.Services.AddNetProPayments<BuildingBlocksDbContext>(options =>
            // {
            //    // Set callbacks here for payment events
            // });

            // MSGraph
            builder.Services.AddMSGraphAD(_ => { });

            // builder.Services.AddMSGraphB2C(options => { });

            // Caching
            builder.Services.AddScoped<ICacheManager, PerRequestCacheManager>();

            // TODO: the current framework (NetProGroup.Framework.DependencyInjection)
            // doesn't support open generic types in the dependency injection container.
            // for example, ILogger, ILogger<MyType> , we're going to keep it that for now.
            builder.Services.AddTransient(typeof(IExcelTemplateService<>), typeof(ExcelTemplateService<>));

            // Register services from assemblies
            var assemblies = new List<Assembly>();

            assemblies.Add(typeof(DataManager.AutoMapper.ApplicationProfile).Assembly);
            assemblies.Add(typeof(TrustDbContext).Assembly);
            assemblies.Add(typeof(IPaymentProvider).Assembly);
            assemblies.Add(typeof(Import.Interfaces.IImport).Assembly);
            assemblies.Add(typeof(IDataMigrationsDataManager).Assembly);
            assemblies.Add(typeof(ExcelTemplateService<>).Assembly);

            builder.Services.RegisterServices(assemblies.ToArray());

            // Register custom repositories
            builder.Services.RegisterCustomRepositories();

            builder.Services.AddEmailLoggerDecorators();
        }

        /// <summary>
        /// Configures the API with defaults.
        /// </summary>
        /// <param name="builder">Instance of the WebApplicationBuilder.</param>
        public static void SetupDefaults(this WebApplicationBuilder builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            builder.Services.Configure<AppSettings>(builder.Configuration);
            builder.Services.Configure<TrustOfficeOptions>(builder.Configuration.GetSection(TrustOfficeOptions.TrustOffice));
            builder.Services.Configure<DataSyncOptions>(builder.Configuration.GetSection(DataSyncOptions.DataSync));
            builder.Services.Configure<GoLiveOptions>(builder.Configuration.GetSection(GoLiveOptions.GoLive));

            builder.Services.TryAddSingleton<IHttpContextAccessor, HttpContextAccessor>();

            if (builder.Configuration.GetValue<string>("AzureAd:ClientId") != null)
            {
                builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme).AddMicrosoftIdentityWebApi(builder.Configuration, "AzureAd");
            }

            builder.Services.AddAutoMapper(
                cfg =>
                {
                    cfg.AddExpressionMapping();
                },

                // Scan for automapper profiles in this assembly
                typeof(DataManager.AutoMapper.ApplicationProfile).Assembly);

            Log.Logger.Information("Environment: {Environment}", builder.Environment.EnvironmentName);
            Globals.Environment = builder.Environment.EnvironmentName.ToLower() switch
            {
                "development" => EnvironmentType.Development,
                "testing" => EnvironmentType.Test,
                "acceptance" => EnvironmentType.Acceptance,
                "staging" => EnvironmentType.Acceptance,
                "production" => EnvironmentType.Production,
                _ => throw new ArgumentException("A valid Environment is required")
            };
        }

        /// <summary>
        /// Setup versioning of the API.
        /// </summary>
        /// <param name="builder">WebApplicationBuilder.</param>
        public static void SetupVersioning(this WebApplicationBuilder builder)
        {
            ArgumentNullException.ThrowIfNull(builder, nameof(builder));

            builder.Services.AddApiVersioning(options =>
            {
                options.AssumeDefaultVersionWhenUnspecified = true;
                options.ReportApiVersions = true;
                options.DefaultApiVersion = new ApiVersion(1, 0);

                // options.ApiVersionReader = ApiVersionReader.Combine(
                //    new UrlSegmentApiVersionReader(),
                //    new HeaderApiVersionReader("X-Api-Version"));
            }).AddApiExplorer(options =>
            {
                options.GroupNameFormat = "'v'VVV";
                options.SubstituteApiVersionInUrl = true;
            });
        }

        /// <summary>
        /// Set up the scheduling of jobs.
        /// </summary>
        /// <param name="builder">WebApplicationBuilder.</param>
        public static void SetupScheduling(this WebApplicationBuilder builder)
        {
            ArgumentNullException.ThrowIfNull(builder, nameof(builder));

            builder.Services.AddHostedService<CronSchedulerService>();
            builder.Services.AddHostedService<QueuedJobService>();
            builder.Services.AddHostedService<AnnouncementsService>();
            builder.Services.AddHostedService<RequestForInformationService>();

            GlobalConfiguration.Configuration.UseInMemoryStorage();
            builder.Services.AddHangfire((config) =>
            {
                config.UseInMemoryStorage();
            });
            builder.Services.AddHangfireServer();
        }

        /// <summary>
        /// Setup the service for executing startup tasks.
        /// </summary>
        /// <param name="builder">WebApplicationBuilder.</param>
        public static void SetupStartupTasks(this WebApplicationBuilder builder)
        {
            ArgumentNullException.ThrowIfNull(builder, nameof(builder));

            builder.Services.AddHostedService<StartupService>();
        }

#if DEBUG
        /// <summary>
        /// Deletes all locks from the database. Use only in debugging.
        /// </summary>
        /// <param name="app">The WebApplication instance.</param>
        public static void ClearLocks(this WebApplication app)
        {
            ArgumentNullException.ThrowIfNull(app, nameof(app));

            using var scope = app.Services.CreateScope();

            var locksRepository = scope.ServiceProvider.GetRequiredService<ILockRepository>();
            locksRepository.Delete(locksRepository.FindAll(), true);
        }
#endif

        private static void AddEmailLoggerDecorators(this IServiceCollection services)
        {
            services.TryAddScoped<SmtpService>();
            services.AddScoped<ISmtpService>(provider =>
            {
                var originalService = provider.GetRequiredService<SmtpService>();
                var logger = provider.GetRequiredService<ILogger<SmtpService>>();
                var dependencyTracker = provider.GetRequiredService<IApplicationInsightsDependencyTracker>();
                return new SmtpServiceLogger(originalService, logger, dependencyTracker);
            });

            services.TryAddScoped<SendGridService>();
            services.AddScoped<ISendGridService>(provider =>
            {
                var originalService = provider.GetRequiredService<SendGridService>();
                var logger = provider.GetRequiredService<ILogger<SendGridService>>();
                var dependencyTracker = provider.GetRequiredService<IApplicationInsightsDependencyTracker>();
                return new SendGridServiceLogger(originalService, logger, dependencyTracker);
            });
        }

        /// <summary>
        /// Registers custom repositories that need explicit registration.
        /// </summary>
        /// <param name="services">The service collection.</param>
        private static void RegisterCustomRepositories(this IServiceCollection services)
        {
            // Register the repository that includes deleted submissions
            services.AddScoped<ISubmissionsIncludingDeletedRepository, SubmissionsIncludingDeletedRepository>();
        }

        /// <summary>
        /// Adds all dependencies for using logging
        /// Copied from framework to test if it works correctly and be able to adjust quickly.
        /// TODO move back to framework when everything works correctly on test.
        /// </summary>
        /// <param name="services">The service collection.</param>
        private static IServiceCollection AddNetProLoggingTemp(this IServiceCollection services)
        {
            var configuration = services.BuildServiceProvider().GetRequiredService<IConfiguration>();

            var slot = configuration.GetValue<string>("Slot");

            var logPath = "./App_Data/logs/logs.txt";
            if (!string.IsNullOrEmpty(slot))
            {
                logPath = $"/home/<USER>/http/RawLogs/Slot_{slot}/AppServiceLog.txt";
            }

            var log = new LoggerConfiguration()
                      .ReadFrom.Configuration(configuration)
                      .WriteTo.File(logPath, rollingInterval: RollingInterval.Day, fileSizeLimitBytes: 20000000, retainedFileTimeLimit: new TimeSpan(30, 0, 0, 0, 0))
                      .WriteTo.ApplicationInsights(services.BuildServiceProvider().GetRequiredService<TelemetryConfiguration>(), TelemetryConverter.Traces)
                      .CreateLogger();

            Log.Logger = log;
            services.AddSingleton<Serilog.ILogger>(log);

            log.Information("Logging configured");

            return services;
        }

        /// <summary>
        /// Gets the specified resource by name as a byte array.
        /// </summary>
        /// <param name="name">Name of the resource to find.</param>
        /// <returns>The resourse as byte array.</returns>
        private static byte[] GetResource(string name)
        {
            var resourceName = Assembly.GetExecutingAssembly().GetManifestResourceNames().Where(x => x.EndsWith(name, StringComparison.OrdinalIgnoreCase)).FirstOrDefault();

            if (resourceName == null)
            {
                throw new ArgumentException($"Resource '{name}' not found.");
            }

            Stream sourceStream = Assembly.GetExecutingAssembly().GetManifestResourceStream(resourceName);
            using (var memoryStream = new MemoryStream())
            {
                sourceStream.CopyTo(memoryStream);
                return memoryStream.ToArray();
            }
        }
    }
}
