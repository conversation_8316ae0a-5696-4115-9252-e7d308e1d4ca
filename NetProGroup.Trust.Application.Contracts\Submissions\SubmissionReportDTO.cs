﻿// <copyright file="SubmissionReportDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Submissions
{
    /// <summary>
    /// Represents a report for a submision.
    /// The frontend application uses this data to build a page that can be downloaded as pdf.
    /// </summary>
    public class SubmissionReportDTO
    {
        /// <summary>
        /// Gets or sets the id of the submission.
        /// </summary>
        public Guid SubmissionId { get; set; }

        /// <summary>
        /// Gets or sets the name/text of the submission.
        /// </summary>
        public string SubmissionName { get; set; }

        /// <summary>
        /// Gets or sets the date/time that the submission was finalized.
        /// </summary>
        public DateTime SubmittedAt { get; set; }

        /// <summary>
        /// Gets or sets the version of the last revision.
        /// </summary>
        public int Revision { get; set; }

        /// <summary>
        /// Gets or sets the text for the header of the report.
        /// </summary>
        public string HeaderText { get; set; }

        /// <summary>
        /// Gets or sets the text for the footer of the report.
        /// </summary>
        public string FooterText { get; set; }

        /// <summary>
        /// Gets or sets the text for the companyinfo of the report.
        /// </summary>
        public string CompanyInfoText { get; set; }

        /// <summary>
        /// Gets or sets the dataset as a collection of key/value pairs holdign all the document info from the FormDocument.
        /// </summary>
        public IDictionary<string, string> DataSet { get; set; }
    }
}
