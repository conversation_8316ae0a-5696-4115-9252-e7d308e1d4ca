﻿// <copyright file="KeyValueFormBuilderDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Forms.Forms;

namespace NetProGroup.Trust.API.Swagger.ResponseTypes
{
    /// <summary>
    /// Represents the top level of a document holding a polymorphic form.
    /// </summary>
    public class KeyValueFormBuilderDTO
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="KeyValueFormBuilderDTO"/> class.
        /// </summary>
        /// <param name="form">The form to be wrapped.</param>
        public KeyValueFormBuilderDTO(KeyValueForm form)
        {
            Form = form;
        }

        /// <summary>
        /// Gets or sets the form.
        /// </summary>
        public KeyValueForm Form { get; set; }
    }
}