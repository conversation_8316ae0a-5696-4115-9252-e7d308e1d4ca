﻿// <copyright file="SettingsAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Trust.Application.Contracts.Errors;
using NetProGroup.Trust.Application.Contracts.Jurisdictions;
using NetProGroup.Trust.Application.Contracts.Settings;
using NetProGroup.Trust.Application.Contracts.Shared;
using NetProGroup.Trust.DataManager.Auditing;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.DataManager.Security;
using NetProGroup.Trust.DataManager.Settings;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.Domain.Shared.Defines;
using NetProGroup.Trust.Domain.Shared.Permissions;
using NetProGroup.Trust.Shared.Jurisdictions;
using Newtonsoft.Json;

namespace NetProGroup.Trust.Application.AppServices.Settings
{
    /// <summary>
    /// Implementation of <see cref="ISettingsAppService"/> for managing settings.
    /// </summary>
    public class SettingsAppService : ISettingsAppService
    {
        private readonly ILogger _logger;
        private readonly ISettingsManager _settingsManager;
        private readonly ISystemAuditManager _systemAuditManager;
        private readonly ISecurityManager _securityManager;
        private readonly IJurisdictionsRepository _jurisdictionsRepository;
        private readonly IMasterClientsRepository _masterClientsRepository;
        private readonly ILegalEntitiesRepository _legalEntityRepository;

        /// <summary>
        /// Initializes a new instance of the <see cref="SettingsAppService"/> class.
        /// </summary>
        /// <param name="logger">The logger instance.</param>
        /// <param name="settingsManager">The datamanager for settings.</param>
        /// <param name="systemAuditManager">Instance of the audit manager.</param>
        /// <param name="securityManager">Instance of the security manager.</param>
        /// <param name="jurisdictionsRepository">Repository instance for jurisdictions.</param>
        /// <param name="masterClientsRepository">Repository instance for master clients.</param>
        /// <param name="legalEntityRepository">Repository instance for legal entities.</param>
        /// <param name="mapper">Mapper instance.</param>
        public SettingsAppService(
            ILogger<SettingsAppService> logger,
            ISettingsManager settingsManager,
            ISystemAuditManager systemAuditManager,
            ISecurityManager securityManager,
            IJurisdictionsRepository jurisdictionsRepository,
            IMasterClientsRepository masterClientsRepository,
            ILegalEntitiesRepository legalEntityRepository,
            IMapper mapper)
        {
            _logger = logger;
            _settingsManager = settingsManager;
            _systemAuditManager = systemAuditManager;
            _securityManager = securityManager;
            _jurisdictionsRepository = jurisdictionsRepository;
            _masterClientsRepository = masterClientsRepository;
            _legalEntityRepository = legalEntityRepository;
        }

        /// <summary>
        /// Gets the settings for a specific jurisdiction.
        /// </summary>
        /// <param name="jurisdictionId">Id of the jurisdiction.</param>
        /// <param name="year">Optional year to get settings for.</param>
        /// <returns>A SettingsDTO instance.</returns>
        public async Task<SettingsDTO> ReadSettingsForJurisdictionAsync(Guid jurisdictionId, int? year)
        {
            await _securityManager.RequireManagementAccessToJurisdictionAsync(jurisdictionId);

            var result = new SettingsDTO
            {
                JurisdictionDocumentSettings = await ReadSettingsForJurisdictionAsync<JurisdictionDocumentSettingsDTO>(jurisdictionId),
                FeeSettings = await ReadSettingsForJurisdictionAsync<FeeSettingsDTO>(jurisdictionId),
                STRLatePaymentFeeSettings = new STRLatePaymentFeeSettingsDTO(await _settingsManager.GetSTRLatePaymentFeesForJurisdictionAsync(jurisdictionId, year))
            };

            // If the BFRSubmissionFee setting has any value, return the logs for visualization.
            if (result.FeeSettings != null && result.FeeSettings.BFRSubmissionFee != null)
            {
                // Retrieve Panama logs for the fees.
                var logsRequest = new ListActivityLogRequest()
                {
                    Period = new Period { StartDate = DateTime.UtcNow.AddYears(-7), EndDate = DateTime.UtcNow },
                    EntityId = jurisdictionId,
                    PageSize = int.MaxValue,
                    ActivityType = ActivityLogActivityTypes.PanamaFeeSettingsSaved
                };

                var panamaLogs = await _systemAuditManager.ListActivityLogsAsync(logsRequest);

                // Retrieve the first 5 records
                result.Logs = panamaLogs.ActivityLogItems.Take(5).ToList();
            }

            return result;
        }

        /// <inheritdoc/>
        public async Task<bool> SaveSettingsForJurisdictionAsync(Guid jurisdictionId, string key, string data)
        {
            ArgumentException.ThrowIfNullOrEmpty(key, nameof(key));

            await _securityManager.RequireManagementAccessToJurisdictionAsync(jurisdictionId);

            var jurisdiction = await _jurisdictionsRepository.CheckJurisdictionByIdAsync(jurisdictionId);
            switch (key.ToLower())
            {
                case SettingTypes.Documents:
                    {
                        var settings = JsonConvert.DeserializeObject<JurisdictionDocumentSettingsDTO>(data);
                        if (await SaveSettingsForJurisdictionAsync(settings, jurisdictionId))
                        {
                            await _systemAuditManager.AddActivityLogAsync(jurisdiction, ActivityLogActivityTypes.SettingsSaved, $"Document settings saved for {jurisdiction.Name}.");
                            return true;
                        }

                        break;
                    }

                case SettingTypes.Fees:
                    {
                        var settings = JsonConvert.DeserializeObject<FeeSettingsDTO>(data);
                        if (await SaveSettingsForJurisdictionAsync(settings, jurisdictionId))
                        {
                            await _systemAuditManager.AddActivityLogAsync(jurisdiction, ActivityLogActivityTypes.SettingsSaved, $"Fee settings saved for {jurisdiction.Name}.");
                            return true;
                        }

                        break;
                    }

                case SettingTypes.STR_LatePaymentFees:
                    {
                        var settings = JsonConvert.DeserializeObject<STRLatePaymentFeeSettingsDTO>(data);
                        if (await SaveSettingsForJurisdictionAsync(settings, jurisdictionId))
                        {
                            await _systemAuditManager.AddActivityLogAsync(jurisdiction, ActivityLogActivityTypes.SettingsSaved, $"STRLatePaymentFee settings saved for {jurisdiction.Name}.");
                            return true;
                        }

                        break;
                    }

                default:
                    {
                        throw new BadRequestException($"Settings '{key}' is not supported for Jurisdictions");
                    }
            }

            return false;
        }

        /// <summary>
        /// Saves settings for a specific jurisdiction.
        /// </summary>
        /// <param name="jurisdictionId">Id of the jurisdiction.</param>
        /// <param name="settings">The settings model holding the different setting types.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task SaveSettingsForJurisdictionAsync(Guid jurisdictionId, SettingsDTO settings)
        {
            ArgumentNullException.ThrowIfNull(settings, nameof(settings));

            await _securityManager.RequireManagementAccessToJurisdictionAsync(jurisdictionId);

            var jurisdiction = await _jurisdictionsRepository.CheckJurisdictionByIdAsync(jurisdictionId);

            // Settings for documents per jurisdiction
            if (settings.JurisdictionDocumentSettings != null)
            {
                if (await SaveSettingsForJurisdictionAsync(settings.JurisdictionDocumentSettings, jurisdictionId))
                {
                    await _systemAuditManager.AddActivityLogAsync(jurisdiction, ActivityLogActivityTypes.SettingsSaved, $"Document settings saved for {jurisdiction.Name}.");
                }
            }

            // Settings for fees
            if (settings.FeeSettings != null)
            {
                if (await SaveSettingsForJurisdictionAsync(settings.FeeSettings, jurisdictionId))
                {
                    if (settings.FeeSettings.BFRSubmissionFee != null)
                    {
                        await _systemAuditManager.AddActivityLogAsync(jurisdiction, ActivityLogActivityTypes.PanamaFeeSettingsSaved, $"Fee updated to value '{settings.FeeSettings.BFRSubmissionFee}'.", true);
                    }
                    else
                    {
                        await _systemAuditManager.AddActivityLogAsync(jurisdiction, ActivityLogActivityTypes.SettingsSaved, $"Fee settings saved for {jurisdiction.Name}.");
                    }
                }
            }

            // Settings for late payment fees for Simple Tax Return
            if (settings.STRLatePaymentFeeSettings != null)
            {
                if (await SaveSettingsForJurisdictionAsync(settings.STRLatePaymentFeeSettings, jurisdictionId))
                {
                    await _systemAuditManager.AddActivityLogAsync(jurisdiction, ActivityLogActivityTypes.SettingsSaved, $"STRLatePaymentFee settings saved for {jurisdiction.Name}.");
                }
            }
        }

        /// <inheritdoc/>
        public async Task<object> ReadSettingsForMasterClientAsync(Guid masterClientId, string key)
        {
            ArgumentException.ThrowIfNullOrEmpty(key, nameof(key));

            await _securityManager.RequireManagementUserAsync();

            switch (key.ToLower())
            {
                default:
                    throw new BadRequestException($"Settings '{key}' is not supported for MasterClients");
            }
        }

        /// <inheritdoc/>
        public async Task<bool> SaveSettingsForMasterClientAsync(Guid masterClientId, string key, string data)
        {
            ArgumentException.ThrowIfNullOrEmpty(key, nameof(key));

            await _securityManager.RequireManagementUserAsync();

            switch (key.ToLower())
            {
                default:
                    {
                        throw new BadRequestException($"Settings '{key}' is not supported for MasterClients");
                    }
            }

            // return false;
        }

        /// <inheritdoc />
        public async Task<SettingsDTO> ReadSettingsForCompanyAsync(Guid legalEntityId)
        {
            await _securityManager.RequireManagementAccessToCompanyAsync(legalEntityId);

            var result = new SettingsDTO
            {
                SubmissionSettings = await ReadSettingsForCompanyAsync<SubmissionSettingsDTO>(legalEntityId),
            };

            if (await _securityManager.HasManagementPermissionAsync(WellKnownPermissionNames.Companies_View_Custom_STR_Fee) ||
                await _securityManager.HasManagementPermissionAsync(WellKnownPermissionNames.STRModule_View_Late_Payments))
            {
                result.FeeSettings = await ReadSettingsForCompanyAsync<FeeSettingsDTO>(legalEntityId);
            }

            return result;
        }

        /// <inheritdoc/>
        public async Task<bool> SaveSettingsForCompanyAsync(Guid legalEntityId, string key, string data)
        {
            ArgumentException.ThrowIfNullOrEmpty(key, nameof(key));

            await _securityManager.RequireManagementAccessToCompanyAsync(legalEntityId);

            switch (key.ToLower())
            {
                case "fees":
                    {
                        await _securityManager.RequireOneOfPermissionsAsync(WellKnownPermissionNames.Companies_Set_Custom_STR_Fee, WellKnownPermissionNames.STRModule_Set_Late_Payments);
                        return await SaveSettingsForCompanyAsync<FeeSettingsDTO>(JsonConvert.DeserializeObject<FeeSettingsDTO>(data), legalEntityId);
                    }

                case "submissions":
                    {
                        return await SaveSettingsForCompanyAsync<SubmissionSettingsDTO>(JsonConvert.DeserializeObject<SubmissionSettingsDTO>(data), legalEntityId);
                    }

                default:
                    {
                        throw new BadRequestException($"Settings '{key}' is not supported for Companies");
                    }
            }
        }

        /// <summary>
        /// Saves settings for a specific company.
        /// </summary>
        /// <param name="legalEntityId">Id of the company.</param>
        /// <param name="settings">The settings model holding the different setting types.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task SaveSettingsForCompanyAsync(Guid legalEntityId, SettingsDTO settings)
        {
            ArgumentNullException.ThrowIfNull(settings, nameof(settings));

            // Authorization
            await _securityManager.RequireManagementAccessToCompanyAsync(legalEntityId);

            var legalEntity = await _legalEntityRepository.CheckLegalEntityByIdAsync(legalEntityId);

            // Settings for fees
            if (settings.FeeSettings != null)
            {
                string permission = null;
                if (settings.FeeSettings.STRSubmissionFee.HasValue ||
                    settings.FeeSettings.STRSubmissionFeeInvoiceText != null ||
                    settings.FeeSettings.STRSubmissionLatePaymentFeeExempt.HasValue)
                {
                    permission = WellKnownPermissionNames.Companies_Set_Custom_STR_Fee;
                }

                if (settings.FeeSettings.BFRSubmissionFee.HasValue)
                {
                    permission = WellKnownPermissionNames.Companies_Set_Custom_BFR_Fee;
                }

                if (permission != null)
                {
                    await _securityManager.RequireManagementPermissionForLegalEntityAsync(permission, legalEntityId);
                }

                if (await SaveSettingsForCompanyAsync(settings.FeeSettings, legalEntityId))
                {
                    await _systemAuditManager.AddActivityLogAsync(legalEntity, ActivityLogActivityTypes.SettingsSaved, $"Fee settings saved for {legalEntity.Name}.");
                }
            }

            // Settings for submissions
            if (settings.SubmissionSettings != null)
            {
                if (await SaveSettingsForCompanyAsync(settings.SubmissionSettings, legalEntityId))
                {
                    await _systemAuditManager.AddActivityLogAsync(legalEntity, ActivityLogActivityTypes.SettingsSaved, $"Submission settings saved for {legalEntity.Name}.");
                }
            }
        }

        /// <inheritdoc/>
        public async Task<TSettings> ReadSettingsForCompanyAsync<TSettings>(Guid legalEntityId) where TSettings : IAttributedSettings
        {
            // Authorization
            await _securityManager.RequireManagementAccessToCompanyAsync(legalEntityId);

            return await _settingsManager.ReadSettingsForCompanyAsync<TSettings>(legalEntityId);
        }

        /// <inheritdoc/>
        public async Task<TSettings> ReadSettingsForJurisdictionAsync<TSettings>(Guid jurisdictionId) where TSettings : IAttributedSettings
        {
            await _securityManager.RequireManagementAccessToJurisdictionAsync(jurisdictionId);

            return await _settingsManager.ReadSettingsForJurisdictionAsync<TSettings>(jurisdictionId);
        }

        /// <inheritdoc/>
        public async Task<TSettings> ReadSettingsForMasterClientAsync<TSettings>(Guid masterClientId) where TSettings : IAttributedSettings
        {
            await _securityManager.RequireManagementUserAsync();

            return await _settingsManager.ReadSettingsForMasterClientAsync<TSettings>(masterClientId);
        }

        /// <inheritdoc />
        public async Task<bool> SaveSettingsForCompanyAsync<TSettings>(TSettings settings, Guid legalEntityId) where TSettings : IAttributedSettings
        {
            await _securityManager.RequireManagementAccessToCompanyAsync(legalEntityId);

            if (settings is FeeSettingsDTO)
            {
                // Check if allowed to set STR fee settings for this company
                var legalEntity = await _legalEntityRepository.CheckLegalEntityByIdAsync(legalEntityId, options: o => o.Include(le => le.Jurisdiction));
                ValidateSTRFeeSettings(legalEntity, settings as FeeSettingsDTO);
            }

            return await _settingsManager.SaveSettingsForCompanyAsync(settings, legalEntityId);
        }

        /// <inheritdoc/>
        public async Task<bool> SaveSettingsForJurisdictionAsync<TSettings>(TSettings settings, Guid jurisdictionId) where TSettings : IAttributedSettings
        {
            await _securityManager.RequireManagementAccessToJurisdictionAsync(jurisdictionId);

            return await _settingsManager.SaveSettingsForJurisdictionAsync(settings, jurisdictionId);
        }

        /// <inheritdoc/>
        public async Task<bool> SaveSettingsForMasterClientAsync<TSettings>(TSettings settings, Guid masterClientId) where TSettings : IAttributedSettings
        {
            await _securityManager.RequireManagementUserAsync();

            return await _settingsManager.SaveSettingsForMasterClientAsync(settings, masterClientId);
        }

        private static void ValidateSTRFeeSettings(LegalEntity company, FeeSettingsDTO feeSettings)
        {
            if (feeSettings.STRSubmissionFee.HasValue && feeSettings.STRSubmissionFee.Value > 0)
            {
                string[] allowedTypes = { LegalEntityTypes.IBC, LegalEntityTypes.LLC };
                var entityTypeName = company.EntityTypeName == null ? string.Empty : company.EntityTypeName.ToUpper();

                if (!allowedTypes.Contains(entityTypeName) && company.Jurisdiction.Code.Equals(JurisdictionCodes.Nevis, StringComparison.OrdinalIgnoreCase))
                {
                    // Not allowed for this company
                    throw new BadRequestException(ApplicationErrors.LEGALENTITY_FEE_SETTINGS_NOT_ALLOWED.ToErrorCode(), $"Fee settings for 'STR' not allowed for company of type '{entityTypeName}'");
                }
            }
        }
    }
}
