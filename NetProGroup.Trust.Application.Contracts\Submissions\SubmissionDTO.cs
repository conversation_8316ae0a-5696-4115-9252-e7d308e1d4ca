﻿// <copyright file="SubmissionDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;
using NetProGroup.Trust.Application.Contracts.Forms;
using NetProGroup.Trust.Application.Contracts.RequestsForInformation;
using NetProGroup.Trust.DomainShared.Enums;

namespace NetProGroup.Trust.Application.Contracts.Submissions
{
    /// <summary>
    /// Represents a submission.
    /// </summary>
    public class SubmissionDTO : EntityDTO<Guid>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="SubmissionDTO"/> class.
        /// </summary>
        public SubmissionDTO()
            : base()
        {
        }

        /// <summary>
        /// Gets or sets the name of the submission.
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the financial year of the submission.
        /// </summary>
        public int FinancialYear { get; set; }

        /// <summary>
        /// Gets or sets the date and time that the submission was created.
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Gets or sets the overall status of the submission.
        /// </summary>
        public SubmissionStatus Status { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the submission was finalized.
        /// </summary>
        /// <value>
        /// A nullable <see cref="DateTime"/> representing the finalization timestamp of the submission.
        /// If the submission is not finalized, the value will be <c>null</c>.
        /// </value>
        public DateTime? SubmittedAt { get; set; }

        /// <summary>
        /// Gets or sets the overall status of the submission as text.
        /// </summary>
        [Obsolete("Use Status instead")]
        public string StatusText { get; set; }

        /// <summary>
        /// Gets or sets the email address of the user that created this submission.
        /// </summary>
        public string CreatedByEmail { get; set; }

        /// <summary>
        /// Gets or sets the FormDocument that is created for this submission.
        /// </summary>
        public FormDocumentWithRevisionsDTO FormDocument { get; set; }

        /// <summary>
        /// Gets or sets an indication whether the submission is paid.
        /// </summary>
        /// <remarks>
        /// This will be set when no invoice is required or when the invoice is actually paid.
        /// </remarks>
        public bool? IsPaid { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the submission was paid.
        /// </summary>
        public DateTime? PaidAt { get; set; }

        /// <summary>
        /// Gets or sets the unique ID of the submission to use externally (including the Excel reporting export).
        /// </summary>
        public string ReportId { get; set; }

        /// <summary>
        /// Gets or sets the id of the module that this submission is created for.
        /// </summary>
        public Guid? ModuleId { get; set; }

        /// <summary>
        /// Gets or sets the id of the LegalEntity that this submission is created for.
        /// </summary>
        public Guid LegalEntityId { get; set; }

        /// <summary>
        /// Gets or sets the layout of the Submission.
        /// </summary>
        public string Layout { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the submission was exported.
        /// </summary>
        /// <value>
        /// A nullable <see cref="DateTime"/> representing the export timestamp of the submission.
        /// If the submission is not exported, the value will be <c>null</c>.
        /// </value>
        public DateTime? ExportedAt { get; set; }

        /// <summary>
        /// Gets or sets the name of the legal entity.
        /// </summary>
        public string LegalEntityName { get; set; }

        /// <summary>
        /// Gets or sets the code of the legal entity.
        /// </summary>
        public string LegalEntityCode { get; set; }

        /// <summary>
        /// Gets or sets the ViewPoint code of the legal entity.
        /// </summary>
        public string LegalEntityVPCode { get; set; }

        /// <summary>
        /// Gets or sets the referral office.
        /// </summary>
        public string LegalEntityReferralOffice { get; set; }

        /// <summary>
        /// Gets or sets the code of the master client.
        /// </summary>
        public string MasterClientCode { get; set; }

        /// <summary>
        /// Gets or sets the used payment method.
        /// </summary>
        public string PaymentMethod { get; set; }

        /// <summary>
        /// Gets or sets the date/time that the payment is received.
        /// </summary>
        public DateTime? PaymentReceivedAt { get; set; }

        /// <summary>
        /// Gets or sets the payment reference.
        /// </summary>
        public string PaymentReference { get; set; }

        /// <summary>
        /// Gets or sets the id of the invocie that is created for this submission.
        /// </summary>
        public Guid? InvoiceId { get; set; }

        /// <summary>
        /// Gets or sets the start date for the submission.
        /// </summary>
        /// <remarks>
        /// This property is used for Panama submissions.
        /// </remarks>
        public DateTime? StartsAt { get; set; }

        /// <summary>
        /// Gets or sets the end date for the submission.
        /// </summary>
        /// <remarks>
        /// This property is used for Panama submissions.
        /// </remarks>
        public DateTime? EndsAt { get; set; }

        /// <summary>
        /// Gets or sets the document ids related to the submission.
        /// </summary>
        public List<Guid> DocumentIds { get; set; }

        /// <summary>
        /// Gets or sets the resubmitted date.
        /// </summary>
        public DateTime? InitialSubmittedAt { get; set; }

        /// <summary>
        /// Gets or sets the reopened date.
        /// </summary>
        public DateTime? ReopenedAt { get; set; }

        /// <summary>
        /// Gets or sets the reopen request comments.
        /// </summary>
        public string ReopenRequestComments { get; set; }

        /// <summary>
        /// Gets or sets the requests for information related to the submission.
        /// </summary>
        public List<SubmissionRFIDTO> RequestsForInformation { get; set; }

        /// <summary>
        /// Gets or sets the ViewPoint status of the legal entity.
        /// </summary>
        public string LegalEntityVPStatus { get; set; }

        /// <summary>
        /// Gets or sets the ViewPoint sub status of the legal entity.
        /// </summary>
        public string LegalEntityVPSubStatus { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the entity is marked as deleted.
        /// When true, the entity is considered soft deleted.
        /// </summary>
        public bool IsDeleted { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the entity was marked as deleted.
        /// This property is null if the entity has not been deleted.
        /// </summary>
        public DateTime? DeletedAt { get; set; }
    }
}
