﻿// <copyright file="IFormTemplatesAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Application.Contracts.Forms;

namespace NetProGroup.Trust.Application.AppServices.Forms
{
    /// <summary>
    /// Interface for FormTemplatesAppService.
    /// </summary>
    public interface IFormTemplatesAppService : IScopedService
    {
        /// <summary>
        /// Gets the given form template.
        /// </summary>
        /// <param name="formTemplateId">The id of the template to get.</param>
        /// <param name="allVersions">Denotes whether all versions should be returned or only the last one.</param>
        /// <returns>Returns a versioned Form Template DTO.</returns>
        Task<FormTemplateWithVersionsDTO> GetFormTemplateAsync(Guid formTemplateId, bool allVersions);

        /// <summary>
        /// Gets the form templates for the given jurisdiction and optional module.
        /// </summary>
        /// <param name="jurisdictionId">The id of the jurisdiction to tget the templates for.</param>
        /// <param name="moduleId">The optional moduleid to tet templates for.</param>
        /// <returns>Returns a list of Form Template DTOs.</returns>
        Task<ListFormTemplatesDTO> GetFormTemplatesAsync(Guid jurisdictionId, Guid? moduleId);

        /// <summary>
        /// Creates a new version for a form template.
        /// </summary>
        /// <param name="model">The model with all parameters for the new version.</param>
        /// <returns>Returns a newly created versioned Form Template DTO.</returns>
        Task<FormTemplateWithVersionsDTO> CreateFormTemplateVersionAsync(CreateFormTemplateVersionDTO model);

        /// <summary>
        /// Updates a version of a form template.
        /// </summary>
        /// <param name="model">The model with all parameters for the version.</param>
        /// <returns>Returns the updated versioned Form Template DTO.</returns>
        Task<FormTemplateWithVersionsDTO> UpdateFormTemplateVersionAsync(UpdateFormTemplateVersionDTO model);
    }
}