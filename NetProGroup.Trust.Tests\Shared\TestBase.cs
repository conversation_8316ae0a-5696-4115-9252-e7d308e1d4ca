﻿using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.TestHost;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Framework.DependencyInjection.Extensions;
using NetProGroup.Framework.Mvc.Contexts;
using NetProGroup.Framework.Services.Identity.EFModels;
using NetProGroup.Framework.Services.Identity.Models;
using NetProGroup.Framework.Services.Identity.Repository;
using NetProGroup.Framework.Services.Identity.Services;
using NetProGroup.Trust.Application.AppServices.Jurisdictions;
using NetProGroup.Trust.DataManager.AutoMapper;
using NetProGroup.Trust.DataManager.Bulk;
using NetProGroup.Trust.DataManager.Forms;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Domain.Modules;
using NetProGroup.Trust.Domain.Payments;
using NetProGroup.Trust.Domain.Payments.Invoices;
using NetProGroup.Trust.Domain.Payments.PaymentProvider;
using NetProGroup.Trust.Domain.Payments.Provider;
using NetProGroup.Trust.Domain.Repository;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.Domain.Shared.Modules;
using NetProGroup.Trust.Domain.Shared.Roles;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.Import.Importers;
using NetProGroup.Trust.Payment.Provider;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Template;
using NetProGroup.Trust.Shared.Jurisdictions;

namespace NetProGroup.Trust.Tests.Shared
{
    public abstract class TestBase
    {
        private IRoleRepository _roleRepository;
        private IUserManager _userManager;
        protected TestServer _server;
        protected HttpClient HttpClient;
        protected ApplicationUserDTO ManagementUser;
        protected ApplicationUserDTO ClientUser;
        protected ApplicationUserDTO BasicUser;
        protected MasterClient _masterClient;
        protected Guid _superAdminRoleId = Guid.Parse("{F7B327DB-4633-484A-B8FF-63DEA19FAC02}");
        protected Guid _basicUserRoleId = Guid.Parse("{47F0BEE9-F846-46BD-B136-835BC7CC9E40}");
        private Guid _supportRoleId = Guid.Parse("{0a31ffd4-5249-4341-81df-73058a820076}");
        private Guid _bahamasOwnerRoleId = Guid.Parse("{3f15b6d5-a425-4aff-bf49-39ee9327f213}");
        protected Guid ModuleStrId;
        protected Guid ModuleBfrId;
        protected Guid ModuleEsId;
        protected Guid ModuleEsBviId;
        protected Guid JurisdictionNevisId;
        protected Guid JurisdictionBahamasId;
        protected Guid JurisdictionPanamaId;
        protected Jurisdiction JurisdictionNevis;
        protected Jurisdiction JurisdictionBahamas;
        protected bool SeedFormTemplates = true;
        protected readonly string _clientUserEmail = "<EMAIL>";

        [SetUp]
        public async Task SetupBase()
        {
            var config = new ConfigurationBuilder()
                .SetBasePath(TestContext.CurrentContext.TestDirectory)
                .AddJsonFile("appsettings.json", optional: true)
                .AddJsonFile("appsettings.Test.json", optional: false)
                .AddEnvironmentVariables()
                .AddUserSecrets<TestBase>()
                .Build();

            _server = new TestServer(
                new WebHostBuilder().UseEnvironment("Development")
                                    .UseConfiguration(config)
                                    .UseStartup<Startup>()
                                    .ConfigureTestServices((services) => ConfigureTestServices(services))
            );

            HttpClient = _server.CreateClient();
            _roleRepository = _server.Services.GetRequiredService<IRoleRepository>();
            _userManager = _server.Services.GetRequiredService<IUserManager>();
            await OnSetup();
        }

        public async Task SetUpUserRolesAsync(ApplicationUserDTO user, List<string> roleNames)
        {
            var roles = new List<ApplicationRole>();

            foreach (var roleName in roleNames)
            {
                var role = await _roleRepository.GetRoleByPredicateAsync(r => r.Name == roleName);
                if (role == null)
                {
                    throw new InvalidOperationException($"Role '{roleName}' not found.");
                }

                roles.Add(role);
            }

            var roleIds = roles.Select(r => r.Id).ToList();
            await _userManager.SetUserRolesAsync(user.Id, roleIds, user);
        }

        protected virtual async Task OnSetup()
        {
            var dbcontext = _server.Services.GetRequiredService<TrustDbContext>();
            dbcontext.Database.EnsureDeleted();
            dbcontext.Database.EnsureCreated();
            // dbcontext.Database.Migrate();

            await Seed();
        }

        protected void SetWorkContextUser(ApplicationUserDTO user)
        {
            var workContext = _server.Services.GetRequiredService<IWorkContext>();

            workContext.IdentityUserId = user.Id;
            workContext.User = user;
        }

        protected virtual void ConfigureTestServices(IServiceCollection services)
        {
            services.AddApplicationInsightsTelemetry();

            // Register services from assemblies
            var assemblies = new List<System.Reflection.Assembly>
            {
                typeof(JurisdictionsAppService).Assembly,
                typeof(ApplicationProfile).Assembly,
                typeof(MasterClientImport).Assembly,
                typeof(IJurisdictionsRepository).Assembly,
                typeof(TrustDbContext).Assembly,
                typeof(IPaymentProvider).Assembly,
                typeof(IExcelTemplateService<>).Assembly
            };
            services.RegisterServices(assemblies.ToArray());

            // Register the SimpleBulkOperationProvider for tests
            services.AddSingleton<IBulkOperationProvider, SimpleBulkOperationProvider>();
            services.AddTransient<TestSeeder>();
            services.AddScoped<IWorkContext, TestWorkContext>();
        }

        private async Task Seed()
        {
            var jurisdictionRepository = _server.Services.GetRequiredService<IJurisdictionsRepository>();
            var jurisdictionNevis = (await jurisdictionRepository.FindByConditionAsync(j => j.Code == JurisdictionCodes.Nevis)).Single();
            var jurisdictionBahamas = (await jurisdictionRepository.FindByConditionAsync(j => j.Code == JurisdictionCodes.Bahamas)).Single();
            var jurisdictionPanama = (await jurisdictionRepository.FindByConditionAsync(j => j.Code == JurisdictionCodes.Panama)).Single();

            JurisdictionNevis = jurisdictionNevis;
            JurisdictionBahamas = jurisdictionBahamas;
            JurisdictionNevisId = jurisdictionNevis.Id;
            JurisdictionBahamasId = jurisdictionBahamas.Id;
            JurisdictionPanamaId = jurisdictionPanama.Id;

            await SetupModulesAsync();

            if (SeedFormTemplates)
            {
                await CreateFormTemplatesAsync();
            }

            var seeder = _server.Services.GetRequiredService<TestSeeder>();
            await seeder.RunAsync();

            // Users
            var userManager = _server.Services.GetRequiredService<IUserManager>();

            userManager.CreateRoleAsync(new ApplicationRoleDTO
            {
                Id = _superAdminRoleId,
                Name = WellKnownRoleNames.Common_SuperAdmin,
                DisplayName = WellKnownRoleNames.Common_SuperAdmin
            }).Wait();

            userManager.CreateRoleAsync(new ApplicationRoleDTO
            {
                Id = _basicUserRoleId,
                Name = WellKnownRoleNames.Common_BasicUser,
                DisplayName = WellKnownRoleNames.Common_BasicUser
            }).Wait();

            userManager.CreateRoleAsync(new ApplicationRoleDTO
            {
                Id = WellKnownRoleIds.Client,
                Name = WellKnownRoleNames.Client,
                DisplayName = WellKnownRoleNames.Client
            }).Wait();

            userManager.CreateRoleAsync(new ApplicationRoleDTO
            {
                Id = _supportRoleId,
                Name = WellKnownRoleNames.Common_SupportUser,
                DisplayName = WellKnownRoleNames.Common_SupportUser
            }).Wait();

            userManager.CreateRoleAsync(new ApplicationRoleDTO
            {
                Id = _bahamasOwnerRoleId,
                Name = WellKnownRoleNames.Bahamas_Owner,
                DisplayName = WellKnownRoleNames.Bahamas_Owner
            }).Wait();

            ManagementUser = userManager.CreateUserWithRolesAsync(
                new RegistrationDTO
                {
                    LastName = "User 1",
                    FirstName = "Test",
                    UserName = "<EMAIL>",
                    DisplayName = "Test User 1",
                    Email = "<EMAIL>",
                    ObjectId = Guid.NewGuid(),
                    RoleIds = new List<Guid> { Guid.Parse("{A00D4E6C-DCD6-4FCA-B080-666EEB9AE6CE}") }
                }
            ).Result;

            ClientUser = userManager.CreateUserWithRolesAsync(
                new RegistrationDTO
                {
                    LastName = "User 2",
                    Email = _clientUserEmail,
                    FirstName = "Test",
                    UserName = _clientUserEmail,
                    DisplayName = "Test User 2",
                    ObjectId = Guid.NewGuid(),
                    RoleIds = new List<Guid> { WellKnownRoleIds.Client }
                }
            ).Result;

            BasicUser = userManager.CreateUserWithRolesAsync(
                    new RegistrationDTO
                    {
                        LastName = "User 3",
                        FirstName = "Test",
                        UserName = "<EMAIL>",
                        DisplayName = "Test User 3",
                        Email = "<EMAIL>",
                        ObjectId = Guid.NewGuid(),
                        RoleIds = new List<Guid> { _basicUserRoleId }
                    }
                ).Result;

            _masterClient = _server.Services.GetRequiredService<IMasterClientsRepository>().DbContext.Set<MasterClient>().Add(new MasterClient()
            {
                Code = "TEST_123"
            }).Entity;

            _masterClient.MasterClientUsers.Add(
                new MasterClientUser() { MasterClientId = _masterClient.Id, UserId = ClientUser.Id });

            //payments
            var payment = new Domain.Payments.Payment(new Guid())
            {
                LegalEntityId = new Guid("b4f2b7a4-7c0b-47e3-9a6a-935f5eae9f42"),
                CurrencyId = new Guid("3f8d5cba-3df4-49a7-a1ab-42c4e55f9b5c"),
                Amount = 1500.00M,
                Status = PaymentStatus.Pending,
                CreatedAt = new DateTime(2023, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                UpdatedAt = new DateTime(2023, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                ConcurrencyStamp = Guid.NewGuid(),
                LegalEntity = new LegalEntity
                {
                    Name = "Company LLC",
                    Code = "E-LL",
                    EntityType = LegalEntityType.Company,
                    IncorporationDate = DateTime.UtcNow.AddYears(-2),
                    JurisdictionId = new Guid("BDEF352D-DEDC-4271-888D-EFA168404CE9")
                },
                PaymentInvoices = new List<PaymentInvoice>
                {
                    new()
                    {
                        Invoice = new Invoice(Guid.NewGuid())
                        {
                            InvoiceNr = "*********",
                            Date = new DateTime(2023, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            FinancialYear = 2023,
                            Layout = LayoutConsts.TridentTrust
                        }
                    }
                }
            };

            // Payment Provider
            var paymentProviderService = _server.Services.GetRequiredService<IPaymentProviderRepository>();

            var paymentProvider = new PaymentProvider(Guid.NewGuid())
            {
                Name = "CXPAYKEY",
                Key = "CXPAYKEY",
                BaseUrl = "https://cxpay.transactiongateway.com/api/v2/three-step",
                ApiKey = "2F822Rw39fx762MaV7Yy86jXGTC7sCDy",
                ApiSecret = "2F822Rw39fx762MaV7Yy86jXGTC7sCDy",
                CreatedAt = DateTime.UtcNow, // SYSDATETIME()
                UpdatedAt = DateTime.UtcNow, // SYSDATETIME()
                ConcurrencyStamp = Guid.NewGuid() // NEWID()
            };

            paymentProviderService.Insert(paymentProvider);
            paymentProviderService.SaveChanges();

            // Payment Transaction
            var paymentTransactionService = _server.Services.GetRequiredService<IPaymentTransactionRepository>();

            var paymentTransaction = new PaymentTransaction(new Guid("78fcb820-4ba6-4383-a07e-a798658ad020"))
            {
                Payment = payment,
                PaymentProvider = paymentProvider,
                Result = "Pending", // Result of the transaction
                ResultCode = "100", // Assuming 100 represents 'In Progress'
                ResultMessage = "Transaction is currently in progress",
                TransactionId = "TXN*********0", // Unique transaction ID
                Status = "In Progress", // Transaction status
                CardDigits = "1234", // Last 4 digits of the card used
                ProcessCreatedAt = DateTime.UtcNow, // Date and time when the transaction was created
                PaidAt = null, // Set when payment is completed
                IsFinished = false, // Transaction is not yet finished
                FirstName = "John", // First name of the customer
                LastName = "Doe", // Last name of the customer
                Address = "123 Main St", // Customer address
                City = "New York", // Customer city
                State = "NY", // Customer state
                ZipCode = "10001", // Customer ZIP code
                Company = "Customer's Company", // Company name if applicable
                PhoneNumber = "555-1234", // Customer phone number
                PaymentProviderId = paymentProvider.Id,
                Email = "<EMAIL>", // Customer email
                CreatedAt = DateTime.UtcNow, // SYSDATETIME() equivalent for created timestamp
                UpdatedAt = DateTime.UtcNow, // SYSDATETIME() equivalent for updated timestamp
                ConcurrencyStamp = Guid.NewGuid() // NEWID() equivalent for concurrency control
            };

            // Insert the new PaymentTransaction into the repository
            paymentTransactionService.Insert(paymentTransaction);
            paymentTransactionService.SaveChanges();
        }

        /// <summary>
        /// Setup of all modules.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        private async Task SetupModulesAsync()
        {
            await CreateModuleAsync(ModuleKeyConsts.BODirectors, "BO/Directors");

            // Nevis
            ModuleStrId = await CreateModuleAsync(ModuleKeyConsts.SimplifiedTaxReturn, "Simplified Tax Return");

            // Panama
            ModuleBfrId = await CreateModuleAsync(ModuleKeyConsts.BasicFinancialReportPanama, "Basic Financial Report");

            // Bahamas
            ModuleEsId = await CreateModuleAsync(ModuleKeyConsts.EconomicSubstanceBahamas, "Economic Substance");

            // BVI
            ModuleEsBviId = await CreateModuleAsync(ModuleKeyConsts.EconomicSubstanceBVI, "Economic Substance");
        }

        /// <summary>
        /// Creates the form templates for the various years.
        /// </summary>
        /// <returns></returns>
        private async Task CreateFormTemplatesAsync()
        {
            var years = new int[] { 2019, 2020, 2021, 2022, 2023, 2024 };

            var formsDataManager = _server.Services.GetRequiredService<IFormsDataManager>();
            await formsDataManager.CreateFormTemplatesForJurisdictionAsync(JurisdictionNevisId, ModuleKeyConsts.SimplifiedTaxReturn, years);
            await formsDataManager.CreateFormTemplatesForJurisdictionAsync(JurisdictionBahamasId, ModuleKeyConsts.EconomicSubstanceBahamas, years);
            await formsDataManager.CreateFormTemplatesForJurisdictionAsync(JurisdictionPanamaId, ModuleKeyConsts.BasicFinancialReportPanama, years);
        }

        /// <summary>
        /// Creates a module if it doesn't exist yet.
        /// </summary>
        /// <param name="key">The key for the module.</param>
        /// <param name="name">The name for the module.</param>
        /// <returns>The id of the module.</returns>
        private async Task<Guid> CreateModuleAsync(string key, string name)
        {
            var repository = _server.Services.GetRequiredService<IModulesRepository>();

            var existing = await repository.FindFirstOrDefaultByConditionAsync(x => x.Key == key);
            if (existing == null)
            {
                var toDb = new Module(Guid.NewGuid(), key, name);
                await repository.InsertAsync(toDb, saveChanges: true);
                return toDb.Id;
            }
            else
            {
                return existing.Id;
            }
        }
    }
}
