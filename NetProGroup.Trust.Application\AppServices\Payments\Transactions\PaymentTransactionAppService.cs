// <copyright file="PaymentTransactionAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Application.Contracts.Payments;
using NetProGroup.Trust.Application.Contracts.Payments.Processor;
using NetProGroup.Trust.Application.Contracts.Payments.Request;
using NetProGroup.Trust.Application.Contracts.Payments.Transactions;
using NetProGroup.Trust.DataManager.Payments;

namespace NetProGroup.Trust.Application.AppServices.Payments.Transactions
{
    /// <summary>
    /// Provides application services for managing payments transactions, acting as a facade between the presentation layer and the data manager.
    /// </summary>
    public class PaymentTransactionAppService : IPaymentTransactionAppService
    {
        private readonly IPaymentProcessorFacade _paymentProcessorFacade;
        private readonly IPaymentDataManager _paymentDataManager;

        /// <summary>
        /// Initializes a new instance of the <see cref="PaymentTransactionAppService"/> class with the specified payment processor facade.
        /// </summary>
        /// <param name="paymentProcessorFacade">The payment processor facade to be used by the service.</param>
        /// <param name="paymentDataManager">The payment data manager to be used by the service.</param>
        public PaymentTransactionAppService(
            IPaymentProcessorFacade paymentProcessorFacade,
            IPaymentDataManager paymentDataManager)
        {
            _paymentProcessorFacade = paymentProcessorFacade;
            _paymentDataManager = paymentDataManager;
        }

        /// <summary>
        /// Creates a new payment transaction asynchronously.
        /// </summary>
        /// <remarks>
        /// This method starts a new payment transaction by calling the payment processor facade.
        /// The payment details are provided in the <paramref name="createTransactionRequestDto"/> parameter.
        /// </remarks>
        /// <param name="createTransactionRequestDto">The data transfer object containing the details of the payment to be created, such as amount, currency, and recipient.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the created <see cref="CreateTransactionResponseDTO"/>.</returns>
        public Task<CreateTransactionResponseDTO> AddPaymentTransactionAsync(CreateTransactionRequestDTO createTransactionRequestDto)
            => _paymentProcessorFacade.HandleStartPaymentAsync(createTransactionRequestDto);

        /// <summary>
        /// Submits a payment transaction asynchronously.
        /// </summary>
        /// <param name="createPaymentRequestDto">The payment request containing details necessary to submit the transaction, such as amount, currency, payment method, and payer information.</param>
        /// <returns>
        /// A task that represents the asynchronous operation. The task result contains the <see cref="SubmitPaymentResponseDTO"/> which includes the outcome of the payment transaction submission.
        /// </returns>
        /// <remarks>
        /// This method is intended to handle the submission of payment transactions.
        /// Currently, it is not implemented and will throw a <see cref="NotImplementedException"/>
        /// when called. Implementing this method will involve integrating with payment processing systems and handling various transaction scenarios.
        /// </remarks>
        /// <example>
        /// Here’s an example of how to use this method once implemented:
        /// <code>
        /// var paymentRequest = new SubmitPaymentRequestDTO { /* populate with payment details */ };
        /// var result = await _paymentService.SubmitPaymentTransactionAsync(paymentRequest);
        /// if (result.IsSuccessful)
        /// {
        ///     // Handle successful payment submission
        /// }
        /// else
        /// {
        ///     // Handle failed payment submission
        /// }
        /// </code>
        /// </example>
        /// <exception cref="NotImplementedException">Thrown when the method is called before being implemented.</exception>
        public async Task<SubmitPaymentResponseDTO> SubmitPaymentTransactionAsync(SubmitPaymentRequestDTO createPaymentRequestDto)
        {
            var response = await _paymentProcessorFacade.HandleCommitPaymentAsync(createPaymentRequestDto);

            if (response.Result == 1)
            {
                await _paymentDataManager.HandleCompletedPaymentByTransactionIdAsync(response.TransactionId, saveChanges: true);
            }

            return response;
        }
    }
}