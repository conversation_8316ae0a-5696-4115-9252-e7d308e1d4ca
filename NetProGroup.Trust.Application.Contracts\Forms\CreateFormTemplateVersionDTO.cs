﻿// <copyright file="CreateFormTemplateVersionDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Forms;

namespace NetProGroup.Trust.Application.Contracts.Forms
{
    /// <summary>
    /// Represents a new form template to create.
    /// </summary>
    public class CreateFormTemplateVersionDTO
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="CreateFormTemplateVersionDTO"/> class.
        /// </summary>
        public CreateFormTemplateVersionDTO()
        {
        }

        /// <summary>
        /// Gets or sets the id of the form template to add this version to.
        /// </summary>
        public Guid FormTemplateId { get; set; }

        /// <summary>
        /// Gets or sets the name of the template.
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the version of the template.
        /// </summary>
        public string Version { get; set; }

        /// <summary>
        /// Gets or sets an optional date that the version can be used.
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// Gets or sets the optional year that this version is for.
        /// </summary>
        public int? Year { get; set; }

        /// <summary>
        /// Gets or sets the FormBuilder for this template.
        /// </summary>
        /// <remarks>
        /// If null, the formbuilder of the previous version will be copied as default.
        /// </remarks>
        public FormBuilder FormBuilder { get; set; }
    }
}
