﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
    <RestorePackagesWithLockFile>true</RestorePackagesWithLockFile>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="App_Data\**" />
    <Compile Remove="Helpers\**" />
    <EmbeddedResource Remove="App_Data\**" />
    <EmbeddedResource Remove="Helpers\**" />
    <None Remove="App_Data\**" />
    <None Remove="Helpers\**" />
  </ItemGroup>

	<PropertyGroup>
		<NoWarn>$(NoWarn);CA1014;CA1716</NoWarn>
		<!-- CA1014 == CLS Compliancy, not required -->
		<!-- CA1716 == Don't use Shared keyword in namespace. -->
	</PropertyGroup>

  <ItemGroup>
    <None Remove="Resources\trident_logo.jpg" />
    <None Remove="stylecop.json" />
  </ItemGroup>

  <ItemGroup>
    <AdditionalFiles Include="stylecop.json" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Resources\trident_logo.jpg" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Asp.Versioning.Mvc.ApiExplorer" Version="8.1.0" />
    <PackageReference Include="AutoBogus" Version="2.13.1" />
    <PackageReference Include="Hangfire.Core" Version="1.8.15" />
    <PackageReference Include="Hangfire.InMemory" Version="1.0.0" />
    <PackageReference Include="Hangfire.NetCore" Version="1.8.15" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.2" />
    <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore" Version="8.0.14" />
    <PackageReference Include="NCrontab" Version="3.3.3" />
    <PackageReference Include="NetPro.StyleCop.Configuration.Package" Version="1.1.24" />
    <PackageReference Include="NetProGroup.Framework" Version="1.4.5" />
    <PackageReference Include="Serilog.Sinks.ApplicationInsights" Version="4.0.0" />
    <PackageReference Include="StyleCop.Analyzers" Version="1.1.118">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Swashbuckle.AspNetCore.SwaggerGen" Version="6.7.0" />
    <PackageReference Include="System.Configuration.ConfigurationManager" Version="8.0.1" />
    <PackageReference Include="System.IO.Compression.ZipFile" Version="4.3.0" />
    <PackageReference Include="System.Resources.Extensions" Version="9.0.1" />
  </ItemGroup>


  <ItemGroup>
    <ProjectReference Include="..\NetProGroup.Trust.Application.Contracts\NetProGroup.Trust.Application.Contracts.csproj" />
    <ProjectReference Include="..\NetProGroup.Trust.DataManager\NetProGroup.Trust.DataManager.csproj" />
    <ProjectReference Include="..\NetProGroup.Trust.DataMigration\NetProGroup.Trust.DataMigration.csproj" />
    <ProjectReference Include="..\NetProGroup.Trust.Domain\NetProGroup.Trust.Domain.csproj" />
    <ProjectReference Include="..\NetProGroup.Trust.Import\NetProGroup.Trust.Import.csproj" />
    <ProjectReference Include="..\NetProGroup.Trust.Payment\NetProGroup.Trust.Payment.csproj" />
    <ProjectReference Include="..\NetProGroup.Trust.Reports\NetProGroup.Trust.Reports.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Properties\Resources.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>

</Project>
