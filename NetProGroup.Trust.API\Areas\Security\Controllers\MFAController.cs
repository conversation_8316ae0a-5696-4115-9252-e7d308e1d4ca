﻿// <copyright file="MFAController.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Mvc;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.API.Controllers;
using NetProGroup.Trust.Application.Contracts.Users;
using NetProGroup.Trust.Application.Contracts.Users.MFA;
using Swashbuckle.AspNetCore.Annotations;

namespace NetProGroup.Trust.API.Areas.Security.Controllers
{
    /// <summary>
    /// Controller for MFA (Multi Factor Authentication).
    /// </summary>
    [ApiController]
    [Area("Security")]
    [Route("api/v{version:apiVersion}/[area]/[Controller]")]
    public class MFAController : TrustAPIControllerBase
    {
        private readonly ILogger _logger;
        private readonly IUsersAppService _usersAppService;

        /// <summary>
        /// Initializes a new instance of the <see cref="MFAController"/> class.
        /// </summary>
        /// <param name="logger">The logger instance.</param>
        /// <param name="usersAppService">Instance of UsersAppService.</param>
        public MFAController(
            ILogger<MFAController> logger,
            IUsersAppService usersAppService)
            : base(logger)
        {
            _logger = logger;
            _usersAppService = usersAppService;
        }

        /// <summary>
        /// Gets the method for MFA for the user.
        /// </summary>
        /// <remarks>
        /// Returns an empty string if the method is not set yet.
        ///
        /// Sample request:
        ///
        ///     GET /api/security/mfa/users/{userId}/method.
        ///
        /// </remarks>
        /// <param name="userId">Id of the user to the mfa method for.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains a DTO holdng the current method.</returns>
        [HttpGet("users/{userId}/method")]
        [SwaggerOperation(OperationId = "GetMFAMethod")]
        [ProducesResponseType(typeof(GetUserMFAMethodDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetMFAMethod(Guid userId)
        {
            GetUserMFAMethodDTO item = null;

            var result = await ProcessRequestAsync<GetUserMFAMethodDTO>(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(userId, nameof(userId));
                },

                executeAsync: async () =>
                {
                    item = await _usersAppService.GetUserMFAMethodAsync(userId);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Sets the method for MFA for the user.
        /// </summary>
        /// <remarks>
        /// Returns an MFAInfo with the codes to use for configuring the authenticator.
        ///
        /// Sample request:
        ///
        ///     PUT     /api/security/mfa/users/{userId}/method.
        ///
        /// Possible values are:
        /// * authenticator
        /// * email-code.
        /// </remarks>
        /// <param name="userId">Id of the user to set the method for.</param>
        /// <param name="model">The model with info for setting the method.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains a DTO holdng the current method.</returns>
        [HttpPut("users/{userId}/method")]
        [SwaggerOperation(OperationId = "PutMFAMethod")]
        [ProducesResponseType(typeof(MFAInfoDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> PutMFAMethod(Guid userId, SetUserMFAMethodDTO model)
        {
            return await SetMFAMethod(userId, model);
        }

        /// <summary>
        /// Sets the method for MFA for the user.
        /// </summary>
        /// <remarks>
        /// Returns an MFAInfo with the codes to use for configuring the authenticator.
        ///
        /// Sample request:
        ///
        ///     PATCH   /api/security/mfa/users/{userId}/method.
        ///
        /// Possible values are:
        /// * authenticator
        /// * email-code.
        /// </remarks>
        /// <param name="userId">Id of the user to set the method for.</param>
        /// <param name="model">The model with info for setting the method.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains a DTO holdng the current method.</returns>
        [HttpPatch("users/{userId}/method")]
        [SwaggerOperation(OperationId = "PatchMFAMethod")]
        [ProducesResponseType(typeof(MFAInfoDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> PatchMFAMethod(Guid userId, SetUserMFAMethodDTO model)
        {
            return await SetMFAMethod(userId, model);
        }

        /// <summary>
        /// Resets/deletes the MFA info for the given user (for management only).
        /// </summary>
        /// <remarks>
        /// After this, the method to use must be set again and in case of an authenticator it must be re-configured.
        ///
        /// Sample request:
        ///
        ///     DELETE  /api/security/mfa/users/{userId}.
        ///
        /// </remarks>
        /// <param name="userId">Id of the user to reset the MFA for.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        [HttpDelete("users/{userId}")]
        [SwaggerOperation(OperationId = "ResetMFAMethod")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> ResetMFAMethod(Guid userId)
        {
            var result = await ProcessRequestAsync<object>(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(userId, nameof(userId));
                },

                executeAsync: async () =>
                {
                    await _usersAppService.ResetUserMFAInfoAsync(userId);
                });

            return result.AsNoContentResponse();
        }

        /// <summary>
        /// Posts a requests for reset of the MFA of the user.
        /// </summary>
        /// <remarks>
        /// The user will receive an email with a code that needs to be used for cofirmation of the reset.
        ///
        /// Sample request:
        ///
        ///     POST /api/security/mfa/users/{userId}/reset-request.
        ///
        /// </remarks>
        /// <param name="userId">Id of the user to create an MFA reset request for.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        [HttpPost("users/{userId}/reset-request")]
        [SwaggerOperation(OperationId = "RequestMFAReset")]
        [ProducesResponseType(typeof(RequestMFAResetResultDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> RequestMFAReset(Guid userId)
        {
            RequestMFAResetResultDTO item = null;

            var result = await ProcessRequestAsync<RequestMFAResetResultDTO>(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(userId, nameof(userId));
                },

                executeAsync: async () =>
                {
                    item = await _usersAppService.RequestUserMFAResetAsync(userId);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Posts the confirmation for reset of the MFA of the user.
        /// </summary>
        /// <remarks>
        /// The user must enter the code that was received by email.
        /// After this, the method to use must be set again and in case of an authenticator it must be re-configured.
        ///
        /// Sample request:
        ///
        ///     POST /api/security/mfa/users/{userId}/reset-confirmation?code={code}.
        ///
        /// </remarks>
        /// <param name="userId">Id of the user to create an MFA reset request for.</param>
        /// <param name="code">The code that the user entered for the MFA reset confirmation.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        [HttpPost("users/{userId}/reset-confirmation")]
        [SwaggerOperation(OperationId = "ConfirmMFAReset")]
        [ProducesResponseType(typeof(ConfirmMFAResetResultDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> ConfirmMFAReset(Guid userId, string code)
        {
            ConfirmMFAResetResultDTO item = null;

            var result = await ProcessRequestAsync<ConfirmMFAResetResultDTO>(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(userId, nameof(userId));
                },

                executeAsync: async () =>
                {
                    item = await _usersAppService.ConfirmUserMFAResetAsync(new ConfirmMFAResetDTO { UserId = userId, ConfirmationCode = code });
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Sends an email to the user with a verification code for MFA.
        /// </summary>
        /// <remarks>
        /// Returns an MFAInfo with the expiration date for the code.
        ///
        /// Sample request:
        ///
        ///     POST    /api/security/mfa/users/{userId}/email.
        ///
        /// </remarks>
        /// <param name="userId">Id of the user to send an email with the code to.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains a DTO holding the MFA info.</returns>
        [HttpPost("users/{userId}/email")]
        [SwaggerOperation(OperationId = "SendMFAEmail")]
        [ProducesResponseType(typeof(MFAInfoDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> SendMFAEmail(Guid userId)
        {
            MFAInfoDTO item = null;

            var result = await ProcessRequestAsync<MFAInfoDTO>(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(userId, nameof(userId));
                },

                executeAsync: async () =>
                {
                    item = await _usersAppService.RequestMFACodeByEmailAsync(userId);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Verifies the code that the user entered for the MFA.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST    /api/security/mfa/users/{userId}/verification?code={code}.
        ///
        /// </remarks>
        /// <param name="userId">Id of the user to send an email with the code to.</param>
        /// <param name="code">The code that the user entered for the MFA verification.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains a DTO holding the MFA info.</returns>
        [HttpPost("users/{userId}/verification")]
        [SwaggerOperation(OperationId = "VerifyMFACode")]
        [ProducesResponseType(typeof(VerifyMFACodeResultDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> VerifyMFACode(Guid userId, string code)
        {
            VerifyMFACodeResultDTO item = null;

            var result = await ProcessRequestAsync<VerifyMFACodeResultDTO>(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(userId, nameof(userId));
                },

                executeAsync: async () =>
                {
                    item = await _usersAppService.VerifyMFACodeAsync(new VerifyMFACodeDTO { UserId = userId, VerificationCode = code });
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        private async Task<IActionResult> SetMFAMethod(Guid userId, SetUserMFAMethodDTO model)
        {
            MFAInfoDTO item = null;

            var result = await ProcessRequestAsync<MFAInfoDTO>(
                validate: () =>
                {
                    Check.NotNull(model, nameof(model));
                    Check.NotDefaultOrNull<Guid>(model.UserId, nameof(model.UserId));

                    if (model.UserId != userId)
                    {
                        throw new BadHttpRequestException("UserId in model does not match");
                    }
                },

                executeAsync: async () =>
                {
                    _logger.LogInformation("Calling SetUserMFAMethodAsync for user {UserId}", model.UserId);
                    item = await _usersAppService.SetUserMFAMethodAsync(model);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }
    }
}
