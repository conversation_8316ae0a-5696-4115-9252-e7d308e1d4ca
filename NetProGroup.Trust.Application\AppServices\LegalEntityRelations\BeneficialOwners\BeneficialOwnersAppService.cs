﻿// <copyright file="BeneficialOwnersAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using Microsoft.Extensions.Logging;
using NetProGroup.Framework.Mvc.Contexts;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.Application.Contracts.LegalEntityRelations;
using NetProGroup.Trust.Application.Contracts.LegalEntityRelations.BeneficialOwners;
using NetProGroup.Trust.DataManager.Auditing;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.DataManager.LegalEntityRelations;
using NetProGroup.Trust.DataManager.LegalEntityRelations.BeneficialOwners.RequestResponses;
using NetProGroup.Trust.DataManager.Security;
using NetProGroup.Trust.Domain.Modules;
using NetProGroup.Trust.Domain.Shared.Modules;
using X.PagedList;

namespace NetProGroup.Trust.Application.AppServices.LegalEntityRelations.BeneficialOwners
{
    /// <summary>
    /// Application service for BeneficialOwners.
    /// </summary>
    public class BeneficialOwnersAppService : IBeneficialOwnersAppService
    {
        private readonly ILogger _logger;
        private readonly IMapper _mapper;
        private readonly IWorkContext _workContext;
        private readonly ISystemAuditManager _systemAuditManager;
        private readonly IBeneficialOwnersDataManager _dataManager;
        private readonly IModulesRepository _modulesRepository;
        private readonly ILegalEntityModulesRepository _legalEntitiesModulesRepository;
        private readonly ISecurityManager _securityManager;

        /// <summary>
        /// Initializes a new instance of the <see cref="BeneficialOwnersAppService"/> class.
        /// </summary>
        /// <param name="logger">Instance of the logger.</param>
        /// <param name="mapper">Instance of the mapper.</param>
        /// <param name="workContext">The current workcontext.</param>
        /// <param name="systemAuditManager">The AuditManager for logging activities.</param>
        /// <param name="dataManager">The DataManager to use.</param>
        /// <param name="modulesRepository">The repository for modules.</param>
        /// <param name="legalEntitiesModulesRepository">The repository for legal entities modules.</param>
        /// <param name="securityManager">The security manager.</param>
        public BeneficialOwnersAppService(ILogger<BeneficialOwnersAppService> logger,
                                          IMapper mapper,
                                          IWorkContext workContext,
                                          ISystemAuditManager systemAuditManager,
                                          IBeneficialOwnersDataManager dataManager,
                                          IModulesRepository modulesRepository,
                                          ILegalEntityModulesRepository legalEntitiesModulesRepository,
                                          ISecurityManager securityManager)
        {
            _logger = logger;
            _mapper = mapper;
            _workContext = workContext;
            _systemAuditManager = systemAuditManager;
            _dataManager = dataManager;
            _modulesRepository = modulesRepository;
            _legalEntitiesModulesRepository = legalEntitiesModulesRepository;
            _securityManager = securityManager;
        }

        /// <inheritdoc/>
        public async Task<BeneficialOwnerDTO> GetBeneficialOwnerAsync(string uniqueRelationId)
        {
            Check.NotNullOrWhiteSpace(uniqueRelationId, nameof(uniqueRelationId));

            await CheckModuleEnabledAndAccessToCompany(uniqueRelationId);

            var beneficialOwnerDto = await _dataManager.GetBeneficialOwnerAsync(uniqueRelationId);

            return beneficialOwnerDto;
        }

        /// <inheritdoc/>
        public async Task<BeneficialOwnerComparisonDTO> GetBeneficialOwnerForComparisonAsync(string uniqueRelationId)
        {
            Check.NotNullOrWhiteSpace(uniqueRelationId, nameof(uniqueRelationId));

            await CheckModuleEnabledAndAccessToCompany(uniqueRelationId);

            var beneficialOwnerComparisonDto = await _dataManager.GetBeneficialOwnerForComparisonAsync(uniqueRelationId);

            return beneficialOwnerComparisonDto;
        }

        /// <inheritdoc/>
        public async Task<IPagedList<BeneficialOwnerDTO>> ListBeneficialOwnersAsync(Guid legalEntityId, int pageNumber, int pageSize)
        {
            await _securityManager.RequireClientAccessToCompanyAsync(legalEntityId);
            await CheckModuleEnabledForLegalEntity(legalEntityId);

            var request = new ListBeneficialOwnersRequest { LegalEntityId = legalEntityId, PageNumber = pageNumber, PageSize = pageSize };
            var response = await _dataManager.ListBeneficialOwnersAsync(request);

            return response.BeneficialOwnerItems;
        }

        /// <inheritdoc/>
        public async Task<BeneficialOwnerDTO> RequestUpdateAsync(RequestUpdateDTO requestUpdate)
        {
            ArgumentNullException.ThrowIfNull(requestUpdate, nameof(requestUpdate));

            await CheckModuleEnabledAndAccessToCompany(requestUpdate.UniqueRelationId);

            var request = new DataManager.LegalEntityRelations.RequestResponses.RequestUpdateRequest
            {
                UniqueRelationId = requestUpdate.UniqueRelationId,
                UserId = _workContext.IdentityUserId.Value,
                UpdateRequestType = requestUpdate.UpdateRequestType,
                UpdateRequestComments = requestUpdate.UpdateRequestComments
            };

            var response = await _dataManager.RequestUpdateAsync(request);

            return await _dataManager.GetBeneficialOwnerAsync(requestUpdate.UniqueRelationId);
        }

        /// <inheritdoc/>
        public async Task RequestAssistanceAsync(RequestAssistanceDTO requestAssistance)
        {
            ArgumentNullException.ThrowIfNull(requestAssistance, nameof(requestAssistance));

            await _securityManager.RequireClientAccessToCompanyAsync(requestAssistance.LegalEntityId);
            await CheckModuleEnabledForLegalEntity(requestAssistance.LegalEntityId);

            var request = new DataManager.LegalEntityRelations.RequestResponses.RequestAssistanceRequest
            {
                LegalEntityId = requestAssistance.LegalEntityId,
                UserId = _workContext.IdentityUserId.Value,
                AssistanceRequestType = requestAssistance.AssistanceRequestType,
                AssistanceRequestComments = requestAssistance.AssistanceRequestComments
            };

            var response = await _dataManager.RequestAssistanceAsync(request);

            return;
        }

        /// <inheritdoc/>
        public async Task<BeneficialOwnerDTO> SetConfirmationAsync(string uniqueRelationId)
        {
            Check.NotNullOrWhiteSpace(uniqueRelationId, nameof(uniqueRelationId));

            await CheckModuleEnabledAndAccessToCompany(uniqueRelationId);

            var request = new DataManager.LegalEntityRelations.RequestResponses.ConfirmationRequest
            {
                UniqueRelationId = uniqueRelationId,
                UserId = _workContext.IdentityUserId.Value
            };

            await _dataManager.ConfirmDataAsync(request);

            return await _dataManager.GetBeneficialOwnerAsync(uniqueRelationId);
        }

        /// <inheritdoc />
        public async Task SimulateUpdateSync(string uniqueRelationId, string newName)
        {
            Check.NotNullOrWhiteSpace(uniqueRelationId, nameof(uniqueRelationId));

            await CheckModuleEnabledAndAccessToCompany(uniqueRelationId);

            var current = await _dataManager.GetBeneficialOwnerAsync(uniqueRelationId);
            var request = new SyncBeneficialOwnerRequest();
            request.ChangedBeneficialOwners.Add(new SyncBeneficialOwner
            {
                Name = newName,
                BoDirIncorporationNumber = current.IncorporationNumber,
                UniqueRelationId = current.UniqueRelationCode,
                Nationality = current.Nationality,
                TIN = current.TIN,
                NameOfRegulator = current.NameOfRegulator,
                StockCode = current.StockCode,
                StockExchange = current.StockExchange,
                OfficerTypeCode = current.OfficerTypeCode,
                OfficerTypeName = current.OfficerTypeName,
                Country = current.CountryOfFormation,
                FileType = current.IsIndividual ? "individual" : "company",
                DateOfBirthOrIncorp = current.IsIndividual ? current.DateOfBirth.GetValueOrDefault() : current.DateOfIncorporation.GetValueOrDefault(),
                PlaceOfBirthOrIncorp = current.IsIndividual ? current.PlaceOfBirth : string.Empty,
                CountryOfBirthOrIncorp = current.IsIndividual ? current.CountryOfBirth : string.Empty,
                CountryCodeOfBirthOrIncorp = current.IsIndividual ? current.CountryCodeOfBirth : string.Empty,
                ResidentialOrRegisteredAddress = current.IsIndividual ? current.ResidentialAddress : current.Address,
                JurisdictionOfRegulationOrSovereignState = current.JurisdictionOfRegulator != null ? current.JurisdictionOfRegulator : current.SovereignState,
            });

            await _dataManager.SyncBeneficialOwnersAsync(request);
        }

        private async Task CheckModuleEnabledForLegalEntity(Guid legalEntityId)
        {
            var module = await _modulesRepository.FindFirstOrDefaultByConditionAsync(m => m.Key == ModuleKeyConsts.BODirectors);
            await _legalEntitiesModulesRepository.CheckModuleEnabledForLegalEntityAsync(legalEntityId, module.Id);
        }

        private async Task CheckModuleEnabledAndAccessToCompany(string uniqueRelationId)
        {
            var beneficialOwner = await _dataManager.FindBeneficialOwnerAsync(uniqueRelationId);
            await _securityManager.RequireClientAccessToCompanyAsync(beneficialOwner.LegalEntityId);

            await CheckModuleEnabledForLegalEntity(beneficialOwner.LegalEntityId);
        }
    }
}
