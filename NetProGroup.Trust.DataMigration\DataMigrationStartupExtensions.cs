// <copyright file="DataMigrationStartupExtensions.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using MongoDB.Driver;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Trust.Application.Contracts.Errors;
using NetProGroup.Trust.DataMigration.Logging;
using NetProGroup.Trust.DataMigration.Services.Nevis;
using NetProGroup.Trust.DataMigration.Factories;

namespace NetProGroup.Trust.DataMigration
{
    /// <summary>
    /// Extensions for setting up data migration services.
    /// </summary>
    public static class DataMigrationStartupExtensions
    {
        /// <summary>
        /// Setup the data migration services.
        /// </summary>
        /// <param name="builder">The application builder instance.</param>
        public static void SetupDataMigration(this WebApplicationBuilder builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            // Register core data migration services
            builder.Services.AddScoped<InitialSyncService>();
            builder.Services.AddScoped<EntityMigrationService>();
            builder.Services.AddScoped<DataMigrationOrchestrator>();
            builder.Services.AddScoped<DataMigrationService>();

            // Register specific migration services
            builder.Services.AddScoped<CompanyMigrationServiceWrapper>();
            builder.Services.AddScoped<CompanyMigrationService>();
            builder.Services.AddScoped<EntryMigrationServiceWrapper>();
            builder.Services.AddScoped<EntryMigrationService>();
            builder.Services.AddScoped<InvoiceConfigurationsMigrationService>();
            builder.Services.AddScoped<InvoiceMigrationService>();
            builder.Services.AddScoped<ActivityLogMigrationService>();
            builder.Services.AddScoped<PaymentMigrationService>();
            builder.Services.AddScoped<SubmissionMigrationService>();

            // Register and setup DataMigrationBackgroundService
            builder.Services.AddSingleton<DataMigrationBackgroundService>();
            builder.Services.AddHostedService(sp => sp.GetRequiredService<DataMigrationBackgroundService>());

            // Register the service for writing an excel logbook
            builder.Services.AddScoped<DataMigrationExcelLogService>();

            var configurationSection = builder.Configuration.GetSection("DataMigration");
            var dataMigrationAppSettings = configurationSection.Get<DataMigrationAppSettings>();

            // Register DataMigrationAppSettings
            builder.Services.Configure<DataMigrationAppSettings>(configurationSection);

            // Register MongoDB factory
            SetupMongoDb(builder, dataMigrationAppSettings);
        }

        private static void SetupMongoDb(WebApplicationBuilder builder, DataMigrationAppSettings dataMigrationAppSettings)
        {
            // Register MongoDB factory
            builder.Services.AddSingleton<IMongoDbFactory, MongoDbFactory>();

            // For backward compatibility, register the default MongoDB client and database
            // based on the active jurisdiction
            var activeJurisdiction = dataMigrationAppSettings.ActiveJurisdiction;
            var jurisdictionSettings = dataMigrationAppSettings.JurisdictionSettings[activeJurisdiction];

            // Register MongoDB client
            builder.Services.AddSingleton<IMongoClient>(_ =>
            {
                var connectionString = jurisdictionSettings.MongoConnectionString;
                return new MongoClient(connectionString);
            });

            // Register MongoDB database
            builder.Services.AddScoped(sp =>
            {
                var client = sp.GetRequiredService<IMongoClient>();
                var databaseName = jurisdictionSettings.MongoDatabaseName;
                if (!client.ListDatabaseNames().ToList().Contains(databaseName))
                {
                    throw new APIException(ApplicationErrors.DATABASE_DOES_NOT_EXIST.ToErrorCode(), $"Database {databaseName} does not exist.");
                }
                return client.GetDatabase(databaseName);
            });
        }
    }
}
