﻿// <copyright file="ActivityLogsAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.Exceptions;
using NetProGroup.Framework.Mvc.Contexts;
using NetProGroup.Framework.Services.ActivityLogs.Services;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.Application.Contracts.Audits;
using NetProGroup.Trust.Application.Contracts.Shared;
using NetProGroup.Trust.DataManager.Auditing;
using NetProGroup.Trust.DataManager.Security;
using NetProGroup.Trust.Domain.Shared.Defines;
using X.PagedList;

namespace NetProGroup.Trust.Application.AppServices.Audits
{
    /// <summary>
    /// Implementation for Audits.
    /// </summary>
    public class ActivityLogsAppService : IActivityLogsAppService
    {
        private readonly IWorkContext _workContext;
        private readonly IActivityLogManager _activityLogManager;
        private readonly ISystemAuditManager _systemAuditManager;
        private readonly ISecurityManager _securityManager;

        /// <summary>
        /// Initializes a new instance of the <see cref="ActivityLogsAppService"/> class.
        /// </summary>
        /// <param name="workContext">The context for the current request.</param>
        /// <param name="activityLogManager">Manager for ActivityLog.</param>
        /// <param name="systemAuditManager">Manager for audits.</param>
        /// <param name="securityManager">Manager for security.</param>
        public ActivityLogsAppService(IWorkContext workContext,
            IActivityLogManager activityLogManager,
            ISystemAuditManager systemAuditManager,
            ISecurityManager securityManager)
        {
            _workContext = workContext;
            _activityLogManager = activityLogManager;
            _systemAuditManager = systemAuditManager;
            _securityManager = securityManager;
        }

        /// <summary>
        /// Adds an entry in the activityLog.
        /// </summary>
        /// <param name="model">The model for adding the ActivityLog.</param>
        /// <returns>Task.</returns>
        public async Task AddActivityLogAsync(AddActivityLogDTO model)
        {
            ArgumentNullException.ThrowIfNull(model);

            #region Check the user using the WorkContext

            if (_workContext.User == null)
            {
                throw new BadRequestException("No user in WorkContext");
            }

            await _securityManager.RequireManagementUserAsync();

            #endregion

            #region Check for Wellknown activity

            switch (model.ActivityType.ToLower())
            {
                case ActivityLogActivityTypes.UserAuthenticate:
                    {
                        await _systemAuditManager.AddUserAuthenticatedActivityLogAsync(_workContext.User, saveChanges: true);
                        return;
                    }

                case ActivityLogActivityTypes.UserLogout:
                    {
                        await _systemAuditManager.AddUserLogoutActivityLogAsync(_workContext.User, saveChanges: true);
                        return;
                    }
            }

            #endregion

            #region Check for text

            Check.NotNullOrWhiteSpace(model.Text, nameof(model.Text));
            if (string.IsNullOrWhiteSpace(model.ShortDescription))
            {
                model.ShortDescription = model.Text;
            }

            #endregion

            #region Create entry

            await _activityLogManager.AddActivityLogAsync(
                _workContext.IdentityUserId,
                _workContext.User,
                model.ShortDescription,
                model.Text,
                saveChanges: true,
                activityType: model.ActivityType);

            #endregion
        }

        /// <summary>
        /// Gets all ActivityLogs created in the given period.
        /// </summary>
        /// <param name="period">The period to look for.</param>
        /// <param name="entityId">Optional. The ID of the entity to filter activity logs for.</param>
        /// <param name="pageNumber">Number of the page to get.</param>
        /// <param name="pageSize">Size of the page to return.</param>
        /// <returns>Paged list of ActivityLogItemDTO.</returns>
        public async Task<IPagedList<ActivityLogItemDTO>> GetActivityLogAsync(Period period, Guid? entityId = null, int pageNumber = 1, int pageSize = 20)
        {
            ArgumentNullException.ThrowIfNull(period);

            await _securityManager.RequireManagementUserAsync();

            var request = new ListActivityLogRequest
            {
                Period = period,
                PageNumber = pageNumber,
                PageSize = pageSize,
                EntityId = entityId
            };

            var response = await _systemAuditManager.ListActivityLogsAsync(request);

#pragma warning disable CA1826 // Do not use Enumerable methods on indexable collections
            var activityLogItemDto = response.ActivityLogItems.FirstOrDefault();
#pragma warning restore CA1826 // Do not use Enumerable methods on indexable collections

            if (activityLogItemDto != null)
            {
                await _securityManager.RequireManagementPermissionForAuditEntityAsync(activityLogItemDto.EntityName);
            }

            return response.ActivityLogItems;
        }

        /// <summary>
        /// Gets a specific ActivityLog.
        /// </summary>
        /// <param name="activityLogId">The given id for the ActivityLog.</param>
        /// <returns>ActivityLogItemDTO.</returns>
        public async Task<ActivityLogItemDTO> GetActivityLogAsync(Guid activityLogId)
        {
            await _securityManager.RequireManagementUserAsync();

            var request = new ListActivityLogRequest { ActivityLogId = activityLogId };

            var response = await _systemAuditManager.ListActivityLogsAsync(request);

            if (response.ActivityLogItems.Count > 0)
            {
                var responseActivityLogItem = response.ActivityLogItems[0];

                await _securityManager.RequireManagementPermissionForAuditEntityAsync(responseActivityLogItem.EntityName);

                return responseActivityLogItem;
            }

            return null;
        }
    }
}