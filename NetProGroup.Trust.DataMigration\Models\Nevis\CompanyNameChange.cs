﻿using MongoDB.Bson.Serialization.Attributes;

namespace NetProGroup.Trust.DataMigration.Models.Nevis
{
    /// <summary>
    /// Represents a company name change.
    /// </summary>
    [BsonIgnoreExtraElements] // Ignore any extra elements in the BSON document that are not mapped to properties
    public sealed class CompanyNameChange
    {
        /// <summary>
        /// Gets or sets the name of the company.
        /// </summary>
        [BsonElement("email")]
        public string Email { get; set; }

        /// <summary>
        /// Gets or sets the name of the company.
        /// </summary>
        [BsonElement("originalName")]
        public string OriginalName { get; set; }

        /// <summary>
        /// Gets or sets the name of the company.
        /// </summary>
        [BsonElement("newName")]
        public string NewName { get; set; }

        /// <summary>
        /// Gets or sets the name of the company.
        /// </summary>
        [BsonElement("modulesChanged")]
        public List<string> ModulesChanged { get; set; }

        /// <summary>
        /// Gets or sets the name of the company.
        /// </summary>
        [BsonElement("changedAt")]
        public DateTime ChangedAt { get; set; }
    }
}
