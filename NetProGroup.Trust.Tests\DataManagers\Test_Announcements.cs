using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Trust.Application.Contracts.Announcements;
using NetProGroup.Trust.DataManager.Announcements;
using NetProGroup.Trust.DataManager.InboxItems;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.DataManagers
{
    public class Test_Announcements : TestBase
    {
        private IAnnouncementDataManager _announcementDataManager;
        private IInboxDataManager _inboxDataManager;

        [SetUp]
        public void Setup()
        {
            _announcementDataManager = _server.Services.GetRequiredService<IAnnouncementDataManager>();
            _inboxDataManager = _server.Services.GetRequiredService<IInboxDataManager>();
        }

        [Test]
        public async Task CreateUpdateAnnouncementAsync_InboxMessage_MarkAsRead()
        {
            // Arrange
            CreateUpdateAnnouncementDTO createDto = GetCreateUpdateDTO(_masterClient.Code);

            //Create announcement
            await _announcementDataManager.CreateUpdateAnnouncementAsync(createDto);

            // Retrieve the first unread inbox message for the master client
            var unreadInboxMessage = (await _inboxDataManager.GetInboxMessagesAsync(ClientUser.Id, isRead: false)).Single();

            // Act
            await _inboxDataManager.CreateInboxReadStatusIfNotExistsAsync(unreadInboxMessage.Id, ClientUser.Id, DateTime.UtcNow);

            //Get the inbox messages status 
            var readMessage = (await _inboxDataManager.GetInboxMessagesAsync(ClientUser.Id, isRead: true)).Single();

            // Assert
            readMessage.IsRead.Should().BeTrue("The inbox message should be marked as read after creating the read status.");
        }

        [Test]
        public async Task UpdateAnnouncement_ShouldNotChangeStatusFromScheduledToDraft()
        {
            // Arrange
            CreateUpdateAnnouncementDTO createDto = GetCreateUpdateDTO(_masterClient.Code);

            var announcementId = await _announcementDataManager.CreateUpdateAnnouncementAsync(createDto);
            var announcement =
                (await _announcementDataManager.FilterAnnouncementsAsync(new FilterAnnouncementsDTO())).Single(x =>
                    x.Id == announcementId);
            var originalStatus = announcement.Status;
            originalStatus.Should().Be(AnnouncementStatus.Scheduled);

            // Update the announcement with changes (should not revert to Draft)
            createDto.Id = announcementId;
            createDto.Subject = "Updated Scheduled Announcement";
            createDto.Body = "This is an updated scheduled announcement body.";

            // Act
            await _announcementDataManager.CreateUpdateAnnouncementAsync(createDto);

            // Assert
            var updatedAnnouncement =
                (await _announcementDataManager.FilterAnnouncementsAsync(new FilterAnnouncementsDTO())).Single(x =>
                    x.Id == announcementId);
            updatedAnnouncement.Status.Should().Be(originalStatus,
                "Status should not change from Scheduled to Draft when updating announcement.");
        }

        [Test]
        public async Task FilterAnnouncements_MultipleMasterClients_MapsOnlyCorrectMasterClientCodes()
        {
            // Arrange
            const string masterClientCode1 = "1";
            const string masterClientCode2 = "2";
            var masterClient1 = await CreateMasterClient(masterClientCode1);
            var masterClient2 = await CreateMasterClient(masterClientCode2);
            var createDto1 = GetCreateUpdateDTO(masterClient1.Code);
            var createDto2 = GetCreateUpdateDTO(masterClient2.Code);

            // Create announcement
            var announcement1Id = await _announcementDataManager.CreateUpdateAnnouncementAsync(createDto1);
            await _announcementDataManager.CreateUpdateAnnouncementAsync(createDto2);

            // Act
            var result = await _announcementDataManager.FilterAnnouncementsAsync(new FilterAnnouncementsDTO());

            var actualAnnouncement = result.Should().HaveCount(2).And.ContainSingle(dto => dto.Id == announcement1Id).Subject;
            actualAnnouncement.MasterClientCodes.Should().ContainSingle()
                .Which.Should().Be(masterClientCode1, "The code should match the master client that was set as a recipient.");
        }

        [Test]
        public async Task FilterAnnouncements_MultipleJurisdictions_MapsOnlyCorrectJurisdictionNames()
        {
            // Arrange
            var createDto1 = GetCreateUpdateDTOWithJurisdiction(JurisdictionBahamasId);
            var createDto2 = GetCreateUpdateDTOWithJurisdiction(JurisdictionNevisId);

            // Create announcement
            var announcement1Id = await _announcementDataManager.CreateUpdateAnnouncementAsync(createDto1);
            await _announcementDataManager.CreateUpdateAnnouncementAsync(createDto2);

            // Act
            var result = await _announcementDataManager.FilterAnnouncementsAsync(new FilterAnnouncementsDTO());

            var actualAnnouncement = result.Should().HaveCount(2).And.ContainSingle(dto => dto.Id == announcement1Id).Subject;
            actualAnnouncement.JurisdictionNames.Should().ContainSingle()
                .Which.Should().Be("Bahamas", "The name should match the jurisdiction that was set as a recipient.");
        }

        private CreateUpdateAnnouncementDTO GetCreateUpdateDTO(string masterClientCode)
        {
            var createDto = new CreateUpdateAnnouncementDTO
            {
                Subject = "Scheduled Announcement",
                EmailSubject = "Scheduled Email Subject",
                Body = "This is a scheduled announcement body.",
                IncludeAttachments = false,
                SendNow = false,
                SendAt = DateTime.UtcNow.AddMinutes(10),
                MasterClientCodes = new List<string> { masterClientCode },
                SendToAllMasterClients = false,
                SendToAllJurisdictions = false,
                JurisdictionId = JurisdictionBahamasId,
                UserIds = new List<Guid> { ClientUser.Id }
            };
            return createDto;
        }

        private async Task<MasterClient> CreateMasterClient(string code)
        {
            var masterClient = new MasterClient { Code = code, Name = $"Test Master Client {code}" };

            await _server.Services.GetRequiredService<IMasterClientsRepository>().InsertAsync(masterClient, true);
            return masterClient;
        }
    }
}