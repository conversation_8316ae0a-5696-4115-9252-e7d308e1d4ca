﻿// <copyright file="DateTimeExtensions.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.Exceptions;
using NetProGroup.Framework.Extensions;
using NetProGroup.Trust.Domain.Shared.Modules;

namespace NetProGroup.Trust.DataManager.Extensions
{
    /// <summary>
    /// Extensions for datetime.
    /// </summary>
    public static class DateTimeExtensions
    {
        /// <summary>
        /// Gets the datetime as a nullable type.
        /// </summary>
        /// <param name="value">The datetime to convert.</param>
        /// <returns>The datetime as nullable.</returns>
        public static DateTime? AsNullable(this DateTime? value)
        {
            if (value.GetValueOrDefault() == DateTime.MinValue)
            {
                return null;
            }

            return value;
        }

        /// <summary>
        /// Converts the UTC time to the local time for the jurisdiction.
        /// </summary>
        /// <param name="value">The datetime to convert.</param>
        /// <param name="jurisdictionCode">The code of the jurisdiction for the timezone to convert to.</param>
        /// <returns>The local time.</returns>
        public static DateTime ToLocalTime(this DateTime value, string jurisdictionCode)
        {
            ArgumentException.ThrowIfNullOrWhiteSpace(jurisdictionCode, nameof(jurisdictionCode));

            if (value == DateTime.MinValue)
            {
                return value;
            }

            var utcDateTime = value.AsUtcTime();

            var timeZoneId = GetTimeZoneIdForJurisdictionCode(jurisdictionCode);
            if (string.IsNullOrEmpty(timeZoneId))
            {
                throw new APIException($"TimeZone id could not be determined for juridiction '{jurisdictionCode}'");
            }

            TimeZoneInfo timeZone = TimeZoneInfo.FindSystemTimeZoneById(timeZoneId);

            return TimeZoneInfo.ConvertTimeFromUtc(utcDateTime, timeZone);
        }

        /// <summary>
        /// Converts the time to the Utc time for the jurisdiction of the module.
        /// </summary>
        /// <remarks>
        /// A module is not specifically for a jurisdiction but for submission modules we know the jurisdiction codes.
        /// </remarks>
        /// <param name="value">The datetime to convert.</param>
        /// <param name="moduleKey">The key of the module to use for getting the jurisdiction for the timezone to convert to.</param>
        /// <returns>The Utc time.</returns>
        public static DateTime ToUtcTime(this DateTime value, string moduleKey)
        {
            ArgumentException.ThrowIfNullOrWhiteSpace(moduleKey, nameof(moduleKey));

            if (value == DateTime.MinValue)
            {
                return value;
            }

            var localDateTime = value;

            var jurisdictionCode = MapModuleKeyToJurisdictionCode(moduleKey);

            if (string.IsNullOrEmpty(jurisdictionCode))
            {
                // Not able to map the module to a jurisdiction, just return the datetime
                return value;
            }

            var timeZoneId = GetTimeZoneIdForJurisdictionCode(jurisdictionCode);
            if (string.IsNullOrEmpty(timeZoneId))
            {
                throw new APIException($"TimeZone id could not be determined for module '{moduleKey}'");
            }

            TimeZoneInfo timeZone = TimeZoneInfo.FindSystemTimeZoneById(timeZoneId);

            return TimeZoneInfo.ConvertTimeToUtc(localDateTime, timeZone);
        }

        private static string MapModuleKeyToJurisdictionCode(string moduleKey)
        {
            var code = moduleKey switch
            {
                ModuleKeyConsts.SimplifiedTaxReturn => Shared.Jurisdictions.JurisdictionCodes.Nevis,
                ModuleKeyConsts.BasicFinancialReportPanama => Shared.Jurisdictions.JurisdictionCodes.Panama,
                ModuleKeyConsts.EconomicSubstanceBahamas => Shared.Jurisdictions.JurisdictionCodes.Bahamas,
                _ => string.Empty
            };

            return code;
        }

        private static string GetTimeZoneIdForJurisdictionCode(string jurisdictionCode)
        {
            var timeZoneId = jurisdictionCode switch
            {
                Shared.Jurisdictions.JurisdictionCodes.Nevis => "SA Western Standard Time",
                Shared.Jurisdictions.JurisdictionCodes.BritishVirginIslands => "SA Western Standard Time",
                Shared.Jurisdictions.JurisdictionCodes.Panama => "SA Pacific Standard Time",
                Shared.Jurisdictions.JurisdictionCodes.Bahamas => "Eastern Standard Time",
                Shared.Jurisdictions.JurisdictionCodes.CaymanIslands => "SA Eastern Standard Time",
                _ => string.Empty
            };
            return timeZoneId;
        }
    }
}
