# Data correction pipeline using the shared variable files
name: 'Data Correction - ${{ parameters.targetEnvironment }} - ${{ parameters.scriptName }}'
trigger: none # Manual trigger only

parameters:
- name: targetEnvironment
  displayName: 'Target Environment'
  type: string
  default: 'dev'
  values:
  - dev
  - tst
  - acc
  - prd
- name: scriptName
  displayName: 'Data Correction Script Name'
  type: string
  default: ''
  values:
  - TruncateSyncExcludedLegalEntities.sql
  - update_dates_fromdocumentattributes_TSK-18327.sql
stages:
- stage: DataCorrection
  displayName: 'Data Correction - ${{ parameters.targetEnvironment }}'  
  jobs:
  - template: data-correction-job.yml
    parameters:
      targetEnvironment: ${{ parameters.targetEnvironment }}
      scriptName: ${{ parameters.scriptName }}
      ${{ if or(eq(parameters.targetEnvironment, 'dev'), eq(parameters.targetEnvironment, 'tst')) }}:
        serviceConnection: 'DEV Infra Service Connection'
      ${{ if or(eq(parameters.targetEnvironment, 'acc'), eq(parameters.targetEnvironment, 'prd')) }}:
        serviceConnection: 'PRD Application Service Connection'
