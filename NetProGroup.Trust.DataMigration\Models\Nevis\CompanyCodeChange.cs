﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace NetProGroup.Trust.DataMigration.Models.Nevis
{
    /// <summary>
    /// Represents a company Code change.
    /// </summary>
    [BsonIgnoreExtraElements] // Ignore any extra elements in the BSON document that are not mapped to properties
    public sealed class CompanyCodeChange
    {
        /// <summary>
        /// Gets or sets the name of the company.
        /// </summary>
        [BsonElement("oldCode")]
        public string OldCode { get; set; }

        /// <summary>
        /// Gets or sets the name of the company.
        /// </summary>
        [BsonElement("newCode")]
        public string NewCode { get; set; }

        /// <summary>
        /// Gets or sets the name of the company.
        /// </summary>
        [BsonElement("changedAt")]
        public DateTime ChangedAt { get; set; }
    }
}
