// <copyright file="CronSchedulerService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Hangfire;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using NetProGroup.Trust.Domain.Scheduling;

namespace NetProGroup.Trust.Application.Scheduler.Cron
{
    /// <inheritdoc/>
    public sealed class CronSchedulerService : BackgroundService
    {
        private readonly ILogger _logger;
        private readonly IServiceProvider _serviceProvider;
        private readonly Dictionary<string, JobRunDetails> _jobDetails = new Dictionary<string, JobRunDetails>();

        private IServiceProvider _scopedServiceProvider;
        private IServiceScope _servicescope;

        private IReadOnlyCollection<Type> _cronJobTypes;

        /// <summary>
        /// Initializes a new instance of the <see cref="CronSchedulerService"/> class.
        /// </summary>
        /// <param name="serviceProvider">The serviceprovider to get new scoped services on execution.</param>
        /// <param name="logger">The logger instance.</param>
        public CronSchedulerService(IServiceProvider serviceProvider,
                                    ILogger<CronSchedulerService> logger)
        {
            // Use the container
            _serviceProvider = serviceProvider;

            _logger = logger;
        }

        /// <inheritdoc/>
        public override void Dispose()
        {
            if (_servicescope != null)
            {
                _servicescope.Dispose();
                _servicescope = null;
            }

            base.Dispose();
        }

        /// <summary>
        /// Setup of the task at startup or to update the cron expressions.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task SetupTasksAsync()
        {
            var repository = _scopedServiceProvider.GetRequiredService<IScheduledJobsRepository>();

            _cronJobTypes = this.GetType().Assembly.GetTypes().Where(c => typeof(ICronJob).IsAssignableFrom(c) && c != typeof(ICronJob) && c.IsInterface).ToArray();

            foreach (var cronJobType in _cronJobTypes)
            {
#pragma warning disable CA1031 // Do not catch general exception types
                try
                {
                    ICronJob job = (ICronJob)_scopedServiceProvider.GetService(cronJobType);
                    var id = job.ScheduledJobId;

                    if (id != Guid.Empty)
                    {
                        var scheduledJob = await UpsertScheduledJobAsync(repository, job);

                        AddOrUpdateJob(scheduledJob, job, cronJobType);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Setup of job for type '{JobTypeName}' failed", cronJobType.Name);
                }
#pragma warning restore CA1031 // Do not catch general exception types
            }
        }

        /// <summary>
        /// Runs the job specified in jobDetails.
        /// </summary>
        /// <param name="jobDetails">The details about the job to run.</param>
        public void RunJob(JobRunDetails jobDetails)
        {
            ArgumentNullException.ThrowIfNull(jobDetails, nameof(jobDetails));

            using var servicescope = _serviceProvider.CreateScope();

            var job = (ICronJob)servicescope.ServiceProvider.GetRequiredService(jobDetails.ImplementationType);

            jobDetails.LastRunUtc = DateTime.UtcNow;
            job.RunAsync(jobDetails).Wait();
        }

        /// <inheritdoc/>
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            // Delay before actually starting the service
            await Task.Delay(5000, stoppingToken);

            _servicescope = _serviceProvider.CreateScope();
            _scopedServiceProvider = _servicescope.ServiceProvider;

            await SetupTasksAsync();

            using var tickTimer = new PeriodicTimer(TimeSpan.FromSeconds(10));

            DateTime nextSetupTasks = DateTime.UtcNow.AddSeconds(60);

            while (await tickTimer.WaitForNextTickAsync(stoppingToken))
            {
#pragma warning disable CA1031 // Do not catch general exception types
                try
                {
                    var scheduledJobRepository = _scopedServiceProvider.GetRequiredService<IScheduledJobsRepository>();
                    var scheduledJobsWithTrigger = await scheduledJobRepository.FindByConditionAsync(sj => sj.Trigger);

                    foreach (var scheduledJob in scheduledJobsWithTrigger)
                    {
                        _logger.LogInformation("Trigger scheduled job '{JobKey}'", scheduledJob.Key);
                        RecurringJob.TriggerJob(scheduledJob.Key);
                    }

                    if (nextSetupTasks < DateTime.UtcNow)
                    {
                        await SetupTasksAsync();
                        nextSetupTasks = DateTime.UtcNow.AddSeconds(60);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failure in CronSchedulerService");
                }
#pragma warning restore CA1031 // Do not catch general exception types
            }
        }

        private async Task<ScheduledJob> UpsertScheduledJobAsync(IScheduledJobsRepository repository, ICronJob job)
        {
            var scheduledJob = await repository.GetByIdAsync(job.ScheduledJobId);
            if (scheduledJob == null)
            {
                _logger.LogDebug("Adding job '{JobName}' to ScheduledJobs table", job.ScheduledJobName);

                scheduledJob = new ScheduledJob(job.ScheduledJobId, job.ScheduledJobKey, job.ScheduledJobName, "0 0 * * *") { IsEnabled = false };
                await repository.InsertAsync(scheduledJob, saveChanges: true);
            }
            else
            {
                scheduledJob.Name = job.ScheduledJobName;
                scheduledJob.Key = job.ScheduledJobKey;
                await repository.UpdateAsync(scheduledJob, saveChanges: true);
            }

            return scheduledJob;
        }

        private void AddOrUpdateJob(ScheduledJob scheduledJob, ICronJob job, Type cronJobType)
        {
            if (!_jobDetails.TryGetValue(scheduledJob.Key, out var value))
            {
                _logger.LogDebug("Setting up job '{ScheduledJobName}' with cron expression '{CronExpression}'", job.ScheduledJobName, scheduledJob.CronExpression);
            }
            else
            {
                if (value.CronExpression != scheduledJob.CronExpression)
                {
                    _logger.LogDebug("Updating job '{ScheduledJobName}' to cron expression '{CronExpression}'", job.ScheduledJobName, scheduledJob.CronExpression);
                }
            }

            var jobDetails = new JobRunDetails(scheduledJob.Id, cronJobType, scheduledJob.LastRunAt, scheduledJob.CronExpression);

            _jobDetails[scheduledJob.Key] = jobDetails;

            RecurringJob.AddOrUpdate(scheduledJob.Key, () => RunJob(jobDetails), scheduledJob.CronExpression.Trim());
        }
    }
}
