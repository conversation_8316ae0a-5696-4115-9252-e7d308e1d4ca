using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Trust.Domain.Forms;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.Domain.Shared.Settings;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.Domain.Shared.Modules;
using NetProGroup.Trust.DataMigration.Models.Nevis;

namespace NetProGroup.Trust.DataMigration.Services.Nevis
{
    /// <summary>
    /// Service for managing submission-related operations during data migration.
    /// </summary>
    public class SubmissionMigrationService
    {
        private readonly ISubmissionsRepository _submissionsRepository;
        private readonly IFormTemplatesRepository _formTemplatesRepository;
        private readonly ILogger<SubmissionMigrationService> _logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="SubmissionMigrationService"/> class.
        /// </summary>
        public SubmissionMigrationService(
            ISubmissionsRepository submissionsRepository,
            IFormTemplatesRepository formTemplatesRepository,
            ILogger<SubmissionMigrationService> logger)
        {
            _submissionsRepository = submissionsRepository;
            _formTemplatesRepository = formTemplatesRepository;
            _logger = logger;
        }

        /// <summary>
        /// Sets the properties of a submission.
        /// </summary>
        public static void SetSubmissionProperties(Submission submission,
            Entry entry,
            FormTemplateVersion formTemplateVersion,
            int financialYear,
            LegalEntity legalEntity,
            SubmissionStatus submissionStatus,
            bool submissionIsPaid,
            Guid submissionModuleId)
        {
            ArgumentNullException.ThrowIfNull(submission, nameof(submission));
            ArgumentNullException.ThrowIfNull(formTemplateVersion, nameof(formTemplateVersion));
            ArgumentNullException.ThrowIfNull(entry, nameof(entry));
            ArgumentNullException.ThrowIfNull(legalEntity, nameof(legalEntity));
            submission.CreatedAt = entry.CreatedAt;
            submission.SubmittedAt = entry.SubmittedAt;
            submission.Name = formTemplateVersion.Name;
            submission.FinancialYear = financialYear;
            submission.LegalEntityId = legalEntity.Id;
            submission.ModuleId = submissionModuleId;
            submission.Status = submissionStatus;
            submission.IsPaid = submissionIsPaid;
            submission.PaidAt = entry.Payment?.PaymentReceivedAt;
            submission.ReportId = entry.Id;
            submission.ExportedAt = entry.ExportedAt;

            // See https://dev.azure.com/netprogroup/Trident%20-%20Substance/_git/TNEV%20-%20Client%20Portal?path=/controllers/pdfController.js for the original implementation.
            submission.Layout = entry.UseNewBranding == true ? LayoutConsts.TridentTrust : LayoutConsts.MorningStar;
        }

        /// <summary>
        /// Sets the attributes of a submission.
        /// </summary>
        public static void SetSubmissionAttributes(Submission submission, Entry entry)
        {
            ArgumentNullException.ThrowIfNull(submission, nameof(submission));
            ArgumentNullException.ThrowIfNull(entry, nameof(entry));
            submission.Attributes.SetAttributeValue(SubmissionAttributeKeys.CreatedByEmail, entry.CreatedBy);
            submission.Attributes.SetAttributeValue(SubmissionAttributeKeys.SubmittedByEmail, entry.SubmittedBy);
            submission.Attributes.SetAttributeValue(SubmissionAttributeKeys.ExportedByEmail, entry.ExportedBy);
        }

        /// <summary>
        /// Sets the properties of a form document.
        /// </summary>
        public static void SetFormDocumentProperties(Submission submission, Entry entry, FormTemplateVersion formTemplateVersion, LegalEntity legalEntity, int financialYear, FormDocumentStatus formDocumentStatus, Guid moduleId)
        {
            ArgumentNullException.ThrowIfNull(formTemplateVersion, nameof(formTemplateVersion));
            ArgumentNullException.ThrowIfNull(submission, nameof(submission));
            submission.FormDocument ??= new FormDocument();
            submission.FormDocument.FormTemplateVersion = formTemplateVersion;
            submission.FormDocument.LegalEntity = legalEntity;
            submission.FormDocument.ModuleId = moduleId;
            submission.FormDocument.Name = formTemplateVersion.Name;
            submission.FormDocument.Year = financialYear;
            submission.FormDocument.Status = formDocumentStatus;
        }

        /// <summary>
        /// Gets or creates a submission for the given module, legal entity, and financial year.
        /// </summary>
        public async Task<Submission> GetOrCreateSubmissionAsync(LegalEntity legalEntity, int financialYear, Guid moduleId)
        {
            ArgumentNullException.ThrowIfNull(legalEntity, nameof(legalEntity));
            var submission = await _submissionsRepository.FindFirstOrDefaultByConditionAsync(
                s => s.LegalEntityId == legalEntity.Id && s.ModuleId == moduleId && s.FinancialYear == financialYear,
                submissions => submissions
                    .Include(s => s.FormDocument).ThenInclude(fd => fd.FormTemplateVersion)
                    .Include(s => s.FormDocument).ThenInclude(fd => fd.FormDocumentRevisions)
                    .Include(s => s.Module)
                    .Include(s => s.Attributes)
                    .Include(s => s.Invoice.InvoiceLines)
                    .Include(s => s.Invoice.PaymentInvoices).ThenInclude(pi => pi.Payment.PaymentTransactions)
            );

            if (submission == null)
            {
                _logger.LogDebug("Creating new submission for module {ModuleId}, legal entity {LegalEntityId}, and financial year {FinancialYear}", moduleId, legalEntity.Id, financialYear);
                submission = new Submission();
                await _submissionsRepository.InsertAsync(submission);
            }
            else
            {
                _logger.LogDebug("Updating existing submission {SubmissionId}", submission.Id);
                await _submissionsRepository.UpdateAsync(submission);
            }

            return submission;
        }

        /// <summary>
        /// Gets or creates a form document revision.
        /// </summary>
        public FormDocumentRevision GetOrCreateFormDocumentRevision(Entry entry, Submission submission,
            FormTemplateVersion formTemplateVersion, FormDocumentRevisionStatus formDocumentRevisionStatus)
        {
            ArgumentNullException.ThrowIfNull(formTemplateVersion, nameof(formTemplateVersion));
            ArgumentNullException.ThrowIfNull(submission, nameof(submission));
            var formDocumentRevision = submission.FormDocument.FormDocumentRevisions
                                                 .OrderByDescending(revision => revision.CreatedAt)
                                                 .FirstOrDefault();
            if (formDocumentRevision == null)
            {
                _logger.LogDebug("Creating new form document revision for submission {SubmissionId}", submission.Id);
                formDocumentRevision = new FormDocumentRevision
                {
                    Revision = 0,
                    Status = formDocumentRevisionStatus,
                    DataAsJson = formTemplateVersion.DataAsJson,
                };

                submission.FormDocument.FormDocumentRevisions.Add(formDocumentRevision);
            }
            else
            {
                _logger.LogDebug("Updating existing form document revision {FormDocumentRevisionId}", formDocumentRevision.Id);
                formDocumentRevision.Status = formDocumentRevisionStatus;
            }

            return formDocumentRevision;
        }

        /// <summary>
        /// Gets or creates a template version.
        /// </summary>
        public async Task<FormTemplateVersion> GetOrCreateTemplateVersionAsync(int financialYear, string version,
            Guid jurisdictionId,
            string jurisdictionName,
            string moduleName,
            Guid moduleId)
        {
            var template = await GetOrCreateTemplateAsync(moduleName, moduleId, jurisdictionId, jurisdictionName);

            var templateVersion = FindExistingTemplateVersion(template, financialYear, version, jurisdictionName);
            if (templateVersion == null)
            {
                _logger.LogDebug("Creating new template version for template {TemplateId}", template.Id);
                templateVersion = CreateNewTemplateVersion(template, financialYear, version);
                await _formTemplatesRepository.SaveChangesAsync();
            }
            else
            {
                _logger.LogDebug("Found existing template version {TemplateVersionId}", templateVersion.Id);
            }

            return templateVersion;
        }

        private static FormTemplateVersion CreateNewTemplateVersion(FormTemplate template, int financialYear, string version)
        {
            var newTemplateVersion = new FormTemplateVersion
            {
                Name = $"Simplified Tax Return template {financialYear}",
                StartAt = null,
                Version = version,
                Year = financialYear,
            };

            template.FormTemplateVersions.Add(newTemplateVersion);
            return newTemplateVersion;
        }

        private async Task<FormTemplate> GetOrCreateTemplateAsync(string moduleName, Guid moduleId, Guid jurisdictionId, string jurisdictionName)
        {
            var formTemplates = await _formTemplatesRepository.FindByConditionAsync(
                ft => ft.JurisdictionId == jurisdictionId && ft.ModuleId == moduleId,
                q => q.Include(ft => ft.FormTemplateVersions).Include(ft => ft.Module));

            var templates = formTemplates.ToList();
            switch (templates.Count)
            {
                case > 1:
                    _logger.LogError("Multiple templates found for module {ModuleName} in jurisdiction {JurisdictionName}", moduleName, jurisdictionName);
                    throw new ConstraintException($"Multiple templates found for submission for module '{moduleName}' in jurisdiction '{jurisdictionName}'");
                case 1:
                    _logger.LogTrace("Found existing template for jurisdiction {JurisdictionId} and module {ModuleId}", jurisdictionId, moduleId);
                    return templates.Single();
                default:
                    _logger.LogDebug("Creating new template for jurisdiction {JurisdictionId} and module {ModuleId}", jurisdictionId, moduleId);
                    var newTemplate = new FormTemplate
                    {
                        JurisdictionId = jurisdictionId,
                        Key = ModuleKeyConsts.SimplifiedTaxReturn,
                        ModuleId = moduleId,
                        Name = "Simplified Tax Return template"
                    };

                    await _formTemplatesRepository.InsertAsync(newTemplate);
                    return newTemplate;
            }
        }

        private FormTemplateVersion FindExistingTemplateVersion(FormTemplate template, int financialYear, string version, string jurisdictionName)
        {
            var formTemplateVersions = template.FormTemplateVersions
                .Where(ftv => ftv.Year == financialYear && ftv.Version == version)
                .ToList();

            if (formTemplateVersions.Count > 1)
            {
                _logger.LogError("Multiple versions found for submission for year {FinancialYear} and version {Version} in module {ModuleName} in jurisdiction {JurisdictionName}", financialYear, version, template.Module.Name, jurisdictionName);
                throw new ConstraintException($"Multiple versions found for submission for year '{financialYear}' and version '{version}' " +
                                              $"in module '{template.Module.Name}' in jurisdiction '{jurisdictionName}'");
            }

            return formTemplateVersions.SingleOrDefault();
        }
    }
}