﻿// <copyright file="UpdateFormTemplateVersionDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;
using NetProGroup.Trust.Forms;

namespace NetProGroup.Trust.Application.Contracts.Forms
{
    /// <summary>
    /// Represents form template to update.
    /// </summary>
    public class UpdateFormTemplateVersionDTO : EntityDTO<Guid>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="UpdateFormTemplateVersionDTO"/> class.
        /// </summary>
        public UpdateFormTemplateVersionDTO()
            : base()
        {
        }

        /// <summary>
        /// Gets or sets the name of the template.
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the version of the template.
        /// </summary>
        public string Version { get; set; }

        /// <summary>
        /// Gets or sets an optional date that the version can be used.
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// Gets or sets the optional year that this version is for.
        /// </summary>
        public int? Year { get; set; }

        /// <summary>
        /// Gets or sets the FormBuilder for this template.
        /// </summary>
        /// <remarks>
        /// If null, there is no update of this data.
        /// </remarks>
        public FormBuilder FormBuilder { get; set; }
    }
}
