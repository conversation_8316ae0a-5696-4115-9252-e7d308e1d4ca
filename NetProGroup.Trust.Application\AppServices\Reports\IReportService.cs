// <copyright file="IReportService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.Services.Locks.Models;

namespace NetProGroup.Trust.Application.AppServices.Reports
{
    /// <summary>
    /// Interface for report service.
    /// </summary>
    public interface IReportService
    {
        /// <summary>
        /// Generates the report.
        /// It generates a new report as a byte array and store it in blob storage.
        /// </summary>
        /// <param name="jobLock">The locking object.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task GenerateReportAsync(LockDTO jobLock);
    }
}