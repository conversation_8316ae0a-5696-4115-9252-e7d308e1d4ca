﻿// <copyright file="SystemAuditItemBuilder.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.Extensions;
using NetProGroup.Framework.Services.ActivityLogs.EFModels;
using NetProGroup.Framework.Services.EFAuditing.EFModels;
using NetProGroup.Framework.Services.Identity.Models;
using NetProGroup.Framework.Services.Identity.Services;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.Application.Contracts.Audits;
using NetProGroup.Trust.Domain.Shared.Defines;

namespace NetProGroup.Trust.DataManager.Auditing
{
    /// <summary>
    /// Builder for audit and activity data.
    /// </summary>
    public class SystemAuditItemBuilder : ISystemAuditItemBuilder
    {
        private readonly IUserManager _userManager;

        private readonly Dictionary<Guid, ApplicationUserDTO> _cachedUsers = new Dictionary<Guid, ApplicationUserDTO>();

        /// <summary>
        /// Initializes a new instance of the <see cref="SystemAuditItemBuilder"/> class.
        /// </summary>
        /// <param name="userManager">Instance of the user manager.</param>
        public SystemAuditItemBuilder(IUserManager userManager)
        {
            _userManager = userManager;
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<ActivityLogItemDTO>> BuildActivityLogItemsAsync(IEnumerable<ActivityLog> activityLogs, Dictionary<Guid, List<AuditUnitOfWorkDTO>> unitsOfWork)
        {
            Check.NotNull(activityLogs, nameof(activityLogs));
            Check.NotNull(unitsOfWork, nameof(unitsOfWork));

            var result = new List<ActivityLogItemDTO>();

            #region Build items

            foreach (var activityLog in activityLogs)
            {
                var item = new ActivityLogItemDTO
                {
                    Id = activityLog.Id,
                    ActionDate = activityLog.CreatedAt,
                    ActivityType = activityLog.ActivityType,
                    Action = ActivityTypeDescriptionMapper.GetActionDescriptionFromActivityType(activityLog.ActivityType),
                    Text = activityLog.Text,
                    ShortDescription = activityLog.ShortDescription,
                    EntityName = activityLog.EntityName
                };
                if (activityLog.ContextId.HasValue)
                {
                    if (unitsOfWork.TryGetValue(activityLog.ContextId.Value, out var audits))
                    {
                        item.Audits = audits;
                    }
                }

                var user = await GetUserAsync(activityLog.CreatedByIdentityUserId);
                if (user != null)
                {
                    item.UserName = user.Name + " " + user.Surname;
                    item.EmailAddress = user.Email;
                }

                result.Add(item);
            }

            #endregion

            return result;
        }

        private async Task<ApplicationUserDTO> GetUserAsync(Guid? userId)
        {
            if (userId == null)
            {
                return null;
            }

            if (_cachedUsers.TryGetValue(userId.Value, out ApplicationUserDTO value))
            {
                return value;
            }

            var user = await _userManager.GetUserByIdAsync(userId.Value);
            _cachedUsers.Add(userId.Value, user);
            return user;
        }
    }
}
