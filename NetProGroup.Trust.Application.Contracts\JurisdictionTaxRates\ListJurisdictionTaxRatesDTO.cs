﻿// <copyright file="ListJurisdictionTaxRatesDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.JurisdictionTaxRates
{
    /// <summary>
    /// Represents a list of JurisdictionTaxRateDTO.
    /// </summary>
    public class ListJurisdictionTaxRatesDTO
    {
        /// <summary>
        /// Gets or sets the collection of taxrates.
        /// </summary>
        public IReadOnlyCollection<JurisdictionTaxRateDTO> TaxRates { get; set; } = new List<JurisdictionTaxRateDTO>();
    }
}
